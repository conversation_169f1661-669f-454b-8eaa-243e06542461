// Script de migration pour intégrer le modèle complet obligatoire
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

async function migrateToCompleteModel() {
    console.log('🔄 Migration vers le modèle complet obligatoire...');
    
    const dbPath = path.join(__dirname, 'data/import_management.db');
    
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath, (err) => {
            if (err) {
                console.error('❌ Erreur lors de l\'ouverture de la base de données:', err);
                reject(err);
                return;
            }
            
            console.log('✅ Base de données ouverte');
            
            // Migrations pour ajouter tous les nouveaux champs obligatoires
            const migrations = [
                // Migration 1: Ajouter les nouveaux champs à la table orders
                `ALTER TABLE orders ADD COLUMN customs_duties_total_ttc REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN customs_duties_total_tva REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN customs_duties_total_ht REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN port_fees_total_ttc REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN port_fees_total_tva REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN port_fees_total_ht REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN shipping_company_fees_total_ttc REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN shipping_company_fees_total_tva REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN shipping_company_fees_total_ht REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN other_miscellaneous_expenses_total_ttc REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN other_miscellaneous_expenses_total_tva REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN other_miscellaneous_expenses_total_ht REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN transit_services_expenses_total_ttc REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN transit_services_expenses_total_tva REAL DEFAULT 0`,
                `ALTER TABLE orders ADD COLUMN transit_services_expenses_total_ht REAL DEFAULT 0`,
                
                // Migration 2: Ajouter les nouveaux champs à la table order_items
                `ALTER TABLE order_items ADD COLUMN order_number TEXT`,
                
                // Migration 3: Ajouter les nouveaux champs à la table cost_allocations
                `ALTER TABLE cost_allocations ADD COLUMN cost_category TEXT`,
                `ALTER TABLE cost_allocations ADD COLUMN d3_number TEXT`,
                `ALTER TABLE cost_allocations ADD COLUMN d3_date DATE`,
                `ALTER TABLE cost_allocations ADD COLUMN quittance_number INTEGER`,
                
                // Migration 4: Mettre à jour les valeurs par défaut pour les champs existants
                `UPDATE orders SET operation_type = 'Spare Parts (for maintenance or specific resale)' WHERE operation_type IS NULL OR operation_type = ''`,
                `UPDATE orders SET goods_description = 'SPARE PARTS FOR VEHICLES' WHERE goods_description IS NULL OR goods_description = ''`,
                `UPDATE orders SET fob_currency = 'USD' WHERE fob_currency IS NULL OR fob_currency = ''`,
                
                // Migration 5: Mettre à jour order_number dans order_items
                `UPDATE order_items SET order_number = (SELECT order_number FROM orders WHERE orders.id = order_items.order_id) WHERE order_number IS NULL`,
                
                // Migration 6: Mettre à jour cost_category dans cost_allocations
                `UPDATE cost_allocations SET cost_category = 'CUSTOMS_DUTIES' WHERE cost_type LIKE '%CustomsDuties%'`,
                `UPDATE cost_allocations SET cost_category = 'PORT_FEES' WHERE cost_type LIKE '%ImportDelivery%'`,
                `UPDATE cost_allocations SET cost_category = 'SHIPPING_COMPANY_FEES' WHERE cost_type LIKE '%ShippingAgency%'`,
                `UPDATE cost_allocations SET cost_category = 'OTHER_MISCELLANEOUS_EXPENSES' WHERE cost_type LIKE '%Other%'`,
                
                // Migration 7: Normaliser les cost_type
                `UPDATE cost_allocations SET cost_type = 'CUSTOMS_DUTIES_1' WHERE cost_type = 'CustomsDuties1'`,
                `UPDATE cost_allocations SET cost_type = 'IMPORT_DELIVERY' WHERE cost_type = 'ImportDelivery'`,
                `UPDATE cost_allocations SET cost_type = 'SHIPPING_AGENCY_SERVICES' WHERE cost_type = 'ShippingAgencyServices'`,
                `UPDATE cost_allocations SET cost_type = 'OTHER_MISCELLANEOUS' WHERE cost_type = 'OtherMiscellaneous'`
            ];
            
            let completed = 0;
            const total = migrations.length;
            
            function runNextMigration() {
                if (completed >= total) {
                    console.log('🎉 Toutes les migrations terminées !');
                    
                    // Vérifier les données après migration
                    db.all(`SELECT COUNT(*) as count FROM orders`, (err, rows) => {
                        if (err) {
                            console.error('❌ Erreur lors de la vérification:', err);
                        } else {
                            console.log(`✅ Nombre de commandes après migration: ${rows[0].count}`);
                        }
                        
                        db.close((err) => {
                            if (err) {
                                console.error('❌ Erreur lors de la fermeture:', err);
                                reject(err);
                            } else {
                                console.log('✅ Base de données fermée');
                                resolve();
                            }
                        });
                    });
                    return;
                }
                
                const migration = migrations[completed];
                db.run(migration, (err) => {
                    if (err && !err.message.includes('duplicate column name') && !err.message.includes('no such column')) {
                        console.error(`❌ Erreur migration ${completed + 1}:`, err.message);
                    } else {
                        console.log(`✅ Migration ${completed + 1}/${total} terminée`);
                    }
                    
                    completed++;
                    runNextMigration();
                });
            }
            
            runNextMigration();
        });
    });
}

// Exécuter la migration
migrateToCompleteModel()
    .then(() => {
        console.log('');
        console.log('🚀 Migration terminée avec succès !');
        console.log('');
        console.log('📊 Nouvelles fonctionnalités disponibles :');
        console.log('   ✅ Modèle de données complet selon spécifications');
        console.log('   ✅ Champs obligatoires pour articles (ITEM, part_number, etc.)');
        console.log('   ✅ Avis d\'arrivée complets (voyage, B/L, navire, armateur)');
        console.log('   ✅ Allocations de coûts détaillées par catégorie');
        console.log('   ✅ Calculs automatiques des totaux par type');
        console.log('   ✅ Support des quittances douanes (1 et 2)');
        console.log('   ✅ Gestion des frais portuaires et compagnie maritime');
        console.log('   ✅ Frais divers et services de transit');
        console.log('   ✅ Format monétaire français (5 chiffres après virgule)');
        console.log('');
        console.log('🎯 Vous pouvez maintenant utiliser toutes les fonctionnalités avancées !');
    })
    .catch((error) => {
        console.error('❌ Erreur lors de la migration:', error);
        process.exit(1);
    });

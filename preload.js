const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Exposer les APIs de manière sécurisée au renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  // Authentification
  auth: {
    login: (credentials) => ipcRenderer.invoke('auth:login', credentials),
    register: (userData) => ipcRenderer.invoke('auth:register', userData)
  },

  // Gestion des commandes
  orders: {
    getAll: () => ipcRenderer.invoke('orders:getAll'),
    getById: (id) => ipcRenderer.invoke('orders:getById', id),
    create: (orderData) => ipcRenderer.invoke('orders:create', orderData),
    update: (id, orderData) => ipcRenderer.invoke('orders:update', id, orderData),
    delete: (id) => ipcRenderer.invoke('orders:delete', id)
  },

  // Import/Export Excel
  excel: {
    import: () => ipcRenderer.invoke('excel:import'),
    export: (data) => ipcRenderer.invoke('excel:export', data)
  },

  // Rapports
  reports: {
    getOrderTracking: (filters) => ipcRenderer.invoke('reports:getOrderTracking', filters),
    getCostAnalysis: (orderId) => ipcRenderer.invoke('reports:getCostAnalysis', orderId),
    getMarginAnalysis: () => ipcRenderer.invoke('reports:getMarginAnalysis'),
    getChartData: (chartType) => ipcRenderer.invoke('reports:getChartData', chartType),
    getTopSuppliers: (limit) => ipcRenderer.invoke('reports:getTopSuppliers', limit),
    getCostEvolution: (period) => ipcRenderer.invoke('reports:getCostEvolution', period)
  },

  // Utilitaires
  utils: {
    showMessage: (message) => ipcRenderer.invoke('utils:showMessage', message),
    showError: (error) => ipcRenderer.invoke('utils:showError', error)
  }
});

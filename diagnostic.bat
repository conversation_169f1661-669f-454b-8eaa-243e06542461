@echo off
title Diagnostic - Application de Gestion des Importations
color 0B

echo.
echo ========================================
echo   DIAGNOSTIC SYSTÈME
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 Diagnostic de l'environnement...
echo.

echo ----------------------------------------
echo 1. SYSTÈME D'EXPLOITATION
echo ----------------------------------------
echo OS: %OS%
echo Architecture: %PROCESSOR_ARCHITECTURE%
echo Utilisateur: %USERNAME%
echo Répertoire actuel: %CD%
echo.

echo ----------------------------------------
echo 2. VARIABLES D'ENVIRONNEMENT PATH
echo ----------------------------------------
echo PATH actuel:
echo %PATH%
echo.

echo ----------------------------------------
echo 3. RECHERCHE DE NODE.JS
echo ----------------------------------------

REM Test direct
echo Test de la commande 'node':
node --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ Node.js accessible via PATH
) else (
    echo ❌ Node.js non accessible via PATH
)

echo.
echo Test de la commande 'npm':
npm --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ npm accessible via PATH
) else (
    echo ❌ npm non accessible via PATH
)

echo.
echo Recherche dans les emplacements standards:

REM Chemins d'installation courants
set "SEARCH_PATHS=C:\Program Files\nodejs C:\Program Files (x86)\nodejs %APPDATA%\npm %LOCALAPPDATA%\Programs\nodejs %USERPROFILE%\AppData\Roaming\npm"

for %%p in (%SEARCH_PATHS%) do (
    if exist "%%p\node.exe" (
        echo ✅ Trouvé: %%p\node.exe
        "%%p\node.exe" --version 2>nul
    ) else (
        echo ❌ Non trouvé: %%p\node.exe
    )
)

echo.
echo ----------------------------------------
echo 4. STRUCTURE DU PROJET
echo ----------------------------------------

if exist "package.json" (
    echo ✅ package.json présent
) else (
    echo ❌ package.json manquant
)

if exist "node_modules" (
    echo ✅ node_modules présent
) else (
    echo ❌ node_modules manquant (installation requise)
)

if exist "src" (
    echo ✅ Répertoire src présent
) else (
    echo ❌ Répertoire src manquant
)

if exist "main.js" (
    echo ✅ main.js présent
) else (
    echo ❌ main.js manquant
)

echo.
echo ----------------------------------------
echo 5. CONTENU DU RÉPERTOIRE
echo ----------------------------------------
dir /b

echo.
echo ----------------------------------------
echo 6. RECOMMANDATIONS
echo ----------------------------------------

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PROBLÈME PRINCIPAL: Node.js non installé ou non accessible
    echo.
    echo 📋 SOLUTIONS:
    echo 1. Téléchargez Node.js depuis https://nodejs.org/
    echo 2. Installez la version LTS (recommandée)
    echo 3. Assurez-vous de cocher "Add to PATH" pendant l'installation
    echo 4. Redémarrez votre ordinateur après installation
    echo 5. Ouvrez une nouvelle fenêtre de commande
    echo.
    echo 🔧 ALTERNATIVE:
    echo - Utilisez le script 'lancer-avec-chemin.bat' qui détecte automatiquement Node.js
) else (
    echo ✅ Node.js est installé et accessible
    if not exist "node_modules" (
        echo ⚠️  Les dépendances doivent être installées
        echo Exécutez: npm install
    ) else (
        echo ✅ Environnement prêt pour le lancement
    )
)

echo.
echo ========================================
echo   FIN DU DIAGNOSTIC
echo ========================================
echo.
pause

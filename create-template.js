// Script pour créer un template Excel d'exemple
const ExcelService = require('./src/services/excelService');
const path = require('path');

async function createTemplate() {
    try {
        const excelService = new ExcelService();
        const templatePath = path.join(__dirname, 'template-import.xlsx');
        
        await excelService.createTemplate(templatePath);
        console.log('Template Excel créé avec succès:', templatePath);
        console.log('Vous pouvez utiliser ce fichier comme modèle pour importer des données.');
    } catch (error) {
        console.error('Erreur lors de la création du template:', error.message);
    }
}

createTemplate();

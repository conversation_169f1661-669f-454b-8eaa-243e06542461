class OrderService {
  constructor(database) {
    this.db = database;
  }

  async getAllOrders() {
    try {
      const orders = await this.db.all(`
        SELECT 
          o.*,
          COUNT(oi.id) as items_count
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        GROUP BY o.id
        ORDER BY o.created_at DESC
      `);
      return orders;
    } catch (error) {
      throw error;
    }
  }

  async getOrderById(orderId) {
    try {
      // Récupérer la commande principale
      const order = await this.db.get('SELECT * FROM orders WHERE id = ?', [orderId]);
      
      if (!order) {
        throw new Error('Commande non trouvée');
      }

      // Récupérer les articles
      const items = await this.db.all(
        'SELECT * FROM order_items WHERE order_id = ? ORDER BY item_number',
        [orderId]
      );

      // Récupérer les avis d'arrivée
      const arrivalNotices = await this.db.all(
        'SELECT * FROM arrival_notices WHERE order_id = ?',
        [orderId]
      );

      // Récupérer les allocations de coûts
      const costAllocations = await this.db.all(
        'SELECT * FROM cost_allocations WHERE order_id = ?',
        [orderId]
      );

      return {
        ...order,
        items,
        arrivalNotices,
        costAllocations
      };
    } catch (error) {
      throw error;
    }
  }

  async createOrder(orderData) {
    try {
      const {
        order_number,
        order_date,
        supplier_name,
        shipment_type,
        invoice_number,
        invoice_date,
        from_location,
        to_location,
        operation_type,
        goods_description,
        bank_name,
        lc_number,
        lc_validation_date,
        payment_term,
        price_term,
        quantity_pcs,
        num_containers_20ft,
        num_containers_40ft,
        num_packages,
        exchange_rate_usd_dzd,
        exchange_rate_eur_dzd,
        exchange_rate_yuan_dzd,
        fob_amount_currency,
        fob_currency,
        freight_amount_currency,
        total_cif_currency,
        items = [],
        arrivalNotices = [],
        costAllocations = []
      } = orderData;

      // Calculer les montants automatiquement
      const calculations = this.calculateAmounts({
        fob_amount_currency,
        fob_currency,
        freight_amount_currency,
        total_cif_currency,
        exchange_rate_usd_dzd,
        exchange_rate_eur_dzd,
        exchange_rate_yuan_dzd,
        costAllocations
      });

      // Insérer la commande principale
      const orderResult = await this.db.run(`
        INSERT INTO orders (
          order_number, order_date, supplier_name, shipment_type,
          invoice_number, invoice_date, from_location, to_location,
          operation_type, goods_description, bank_name, lc_number,
          lc_validation_date, payment_term, price_term, quantity_pcs,
          num_containers_20ft, num_containers_40ft, num_packages,
          exchange_rate_usd_dzd, exchange_rate_eur_dzd, exchange_rate_yuan_dzd,
          fob_amount_currency, fob_currency, freight_amount_currency,
          total_cif_currency, fob_amount_dzd, freight_amount_dzd,
          total_cif_dzd, landed_cost_ht, landed_cost_coefficient, total_paid_ttc
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        order_number, order_date, supplier_name, shipment_type,
        invoice_number, invoice_date, from_location, to_location,
        operation_type, goods_description, bank_name, lc_number,
        lc_validation_date, payment_term, price_term, quantity_pcs,
        num_containers_20ft || 0, num_containers_40ft || 0, num_packages,
        exchange_rate_usd_dzd, exchange_rate_eur_dzd, exchange_rate_yuan_dzd,
        fob_amount_currency, fob_currency, freight_amount_currency,
        total_cif_currency, calculations.fob_amount_dzd, calculations.freight_amount_dzd,
        calculations.total_cif_dzd, calculations.landed_cost_ht,
        calculations.landed_cost_coefficient, calculations.total_paid_ttc
      ]);

      const orderId = orderResult.id;

      // Calculer les coûts de revient pour les articles
      const itemsWithCostPrices = this.calculateItemCostPrices({
        fob_amount_currency,
        fob_currency,
        freight_amount_currency,
        total_cif_currency,
        exchange_rate_usd_dzd,
        exchange_rate_eur_dzd,
        exchange_rate_yuan_dzd,
        costAllocations
      }, items);

      // Insérer les articles avec les calculs de coût de revient
      for (const item of itemsWithCostPrices) {
        await this.db.run(`
          INSERT INTO order_items (
            order_id, item_number, part_number, description, quantity, unit_fob, amount,
            unit_fob_dzd, unit_cost_price, total_cost_price, amount_fob_dzd
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          orderId, item.item_number, item.part_number, item.description,
          item.quantity, item.unit_fob, item.amount,
          item.unit_fob_dzd, item.unit_cost_price, item.total_cost_price, item.amount_fob_dzd
        ]);
      }

      // Insérer les avis d'arrivée
      for (const notice of arrivalNotices) {
        await this.db.run(`
          INSERT INTO arrival_notices (order_id, voyage_number, bill_of_lading_number, vessel_name, shipowner, actual_time_of_arrival)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [orderId, notice.voyage_number, notice.bill_of_lading_number, notice.vessel_name, notice.shipowner, notice.actual_time_of_arrival]);
      }

      // Insérer les allocations de coûts
      for (const cost of costAllocations) {
        await this.db.run(`
          INSERT INTO cost_allocations (order_id, cost_type, cost_name, invoice_number, invoice_date, total_ttc, tva, ht, is_manual_entry)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [orderId, cost.cost_type, cost.cost_name, cost.invoice_number, cost.invoice_date, cost.total_ttc, cost.tva, cost.ht, cost.is_manual_entry || false]);
      }

      return await this.getOrderById(orderId);
    } catch (error) {
      throw error;
    }
  }

  async updateOrder(orderId, orderData) {
    try {
      // Vérifier que la commande existe
      const existingOrder = await this.db.get('SELECT id FROM orders WHERE id = ?', [orderId]);
      if (!existingOrder) {
        throw new Error('Commande non trouvée');
      }

      const {
        order_number,
        order_date,
        supplier_name,
        shipment_type,
        invoice_number,
        invoice_date,
        from_location,
        to_location,
        operation_type,
        goods_description,
        bank_name,
        lc_number,
        lc_validation_date,
        payment_term,
        price_term,
        quantity_pcs,
        num_containers_20ft,
        num_containers_40ft,
        num_packages,
        exchange_rate_usd_dzd,
        exchange_rate_eur_dzd,
        exchange_rate_yuan_dzd,
        fob_amount_currency,
        fob_currency,
        freight_amount_currency,
        total_cif_currency,
        items = [],
        arrivalNotices = [],
        costAllocations = []
      } = orderData;

      // Calculer les montants automatiquement
      const calculations = this.calculateAmounts({
        fob_amount_currency,
        fob_currency,
        freight_amount_currency,
        total_cif_currency,
        exchange_rate_usd_dzd,
        exchange_rate_eur_dzd,
        exchange_rate_yuan_dzd,
        costAllocations
      });

      // Mettre à jour la commande principale
      await this.db.run(`
        UPDATE orders SET
          order_number = ?, order_date = ?, supplier_name = ?, shipment_type = ?,
          invoice_number = ?, invoice_date = ?, from_location = ?, to_location = ?,
          operation_type = ?, goods_description = ?, bank_name = ?, lc_number = ?,
          lc_validation_date = ?, payment_term = ?, price_term = ?, quantity_pcs = ?,
          num_containers_20ft = ?, num_containers_40ft = ?, num_packages = ?,
          exchange_rate_usd_dzd = ?, exchange_rate_eur_dzd = ?, exchange_rate_yuan_dzd = ?,
          fob_amount_currency = ?, fob_currency = ?, freight_amount_currency = ?,
          total_cif_currency = ?, fob_amount_dzd = ?, freight_amount_dzd = ?,
          total_cif_dzd = ?, landed_cost_ht = ?, landed_cost_coefficient = ?, total_paid_ttc = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [
        order_number, order_date, supplier_name, shipment_type,
        invoice_number, invoice_date, from_location, to_location,
        operation_type, goods_description, bank_name, lc_number,
        lc_validation_date, payment_term, price_term, quantity_pcs,
        num_containers_20ft || 0, num_containers_40ft || 0, num_packages,
        exchange_rate_usd_dzd, exchange_rate_eur_dzd, exchange_rate_yuan_dzd,
        fob_amount_currency, fob_currency, freight_amount_currency,
        total_cif_currency, calculations.fob_amount_dzd, calculations.freight_amount_dzd,
        calculations.total_cif_dzd, calculations.landed_cost_ht,
        calculations.landed_cost_coefficient, calculations.total_paid_ttc,
        orderId
      ]);

      // Supprimer et recréer les éléments liés
      await this.db.run('DELETE FROM order_items WHERE order_id = ?', [orderId]);
      await this.db.run('DELETE FROM arrival_notices WHERE order_id = ?', [orderId]);
      await this.db.run('DELETE FROM cost_allocations WHERE order_id = ?', [orderId]);

      // Calculer les coûts de revient pour les articles mis à jour
      const itemsWithCostPrices = this.calculateItemCostPrices({
        fob_amount_currency,
        fob_currency,
        freight_amount_currency,
        total_cif_currency,
        exchange_rate_usd_dzd,
        exchange_rate_eur_dzd,
        exchange_rate_yuan_dzd,
        costAllocations
      }, items);

      // Réinsérer les articles avec les calculs de coût de revient
      for (const item of itemsWithCostPrices) {
        await this.db.run(`
          INSERT INTO order_items (
            order_id, item_number, part_number, description, quantity, unit_fob, amount,
            unit_fob_dzd, unit_cost_price, total_cost_price, amount_fob_dzd
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          orderId, item.item_number, item.part_number, item.description,
          item.quantity, item.unit_fob, item.amount,
          item.unit_fob_dzd, item.unit_cost_price, item.total_cost_price, item.amount_fob_dzd
        ]);
      }

      // Réinsérer les avis d'arrivée
      for (const notice of arrivalNotices) {
        await this.db.run(`
          INSERT INTO arrival_notices (order_id, voyage_number, bill_of_lading_number, vessel_name, shipowner, actual_time_of_arrival)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [orderId, notice.voyage_number, notice.bill_of_lading_number, notice.vessel_name, notice.shipowner, notice.actual_time_of_arrival]);
      }

      // Réinsérer les allocations de coûts
      for (const cost of costAllocations) {
        await this.db.run(`
          INSERT INTO cost_allocations (order_id, cost_type, cost_name, invoice_number, invoice_date, total_ttc, tva, ht, is_manual_entry)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [orderId, cost.cost_type, cost.cost_name, cost.invoice_number, cost.invoice_date, cost.total_ttc, cost.tva, cost.ht, cost.is_manual_entry || false]);
      }

      return await this.getOrderById(orderId);
    } catch (error) {
      throw error;
    }
  }

  async deleteOrder(orderId) {
    try {
      const result = await this.db.run('DELETE FROM orders WHERE id = ?', [orderId]);
      if (result.changes === 0) {
        throw new Error('Commande non trouvée');
      }
      return { success: true };
    } catch (error) {
      throw error;
    }
  }

  calculateAmounts(data) {
    const {
      fob_amount_currency,
      fob_currency,
      freight_amount_currency,
      total_cif_currency,
      exchange_rate_usd_dzd,
      exchange_rate_eur_dzd,
      exchange_rate_yuan_dzd,
      costAllocations = [],
      items = []
    } = data;

    let fob_amount_dzd = 0;
    let freight_amount_dzd = 0;
    let total_cif_dzd = 0;

    // Convertir selon la devise
    const getExchangeRate = (currency) => {
      switch (currency?.toUpperCase()) {
        case 'USD': return exchange_rate_usd_dzd || 1;
        case 'EUR': return exchange_rate_eur_dzd || 1;
        case 'YUAN': case 'CNY': return exchange_rate_yuan_dzd || 1;
        default: return 1;
      }
    };

    const rate = getExchangeRate(fob_currency);

    // Calculs de base en DZD
    fob_amount_dzd = (fob_amount_currency || 0) * rate;
    freight_amount_dzd = (freight_amount_currency || 0) * rate;

    // Si total_cif_currency n'est pas fourni, le calculer
    if (!total_cif_currency) {
      total_cif_dzd = fob_amount_dzd + freight_amount_dzd;
    } else {
      total_cif_dzd = (total_cif_currency || 0) * rate;
    }

    // Calculer le coût total des allocations HT et TTC
    const totalCostAllocationsHT = costAllocations.reduce((sum, cost) => sum + (cost.ht || 0), 0);
    const totalCostAllocationsTTC = costAllocations.reduce((sum, cost) => sum + (cost.total_ttc || 0), 0);

    // Calculer le coût d'atterrissage HT (Landed Cost HT)
    // Landed Cost HT = CIF DZD + Total des allocations HT
    const landed_cost_ht = total_cif_dzd + totalCostAllocationsHT;

    // Calculer le coefficient de coût d'atterrissage
    // Landed Cost Coefficient = Landed Cost HT / Total CIF DZD
    const landed_cost_coefficient = total_cif_dzd > 0 ? landed_cost_ht / total_cif_dzd : 0;

    // Calculer le total payé TTC
    const total_paid_ttc = total_cif_dzd + totalCostAllocationsTTC;

    // Calculer le coût de revient par commande
    const cost_price_order = landed_cost_ht;

    return {
      fob_amount_dzd,
      freight_amount_dzd,
      total_cif_dzd,
      landed_cost_ht,
      landed_cost_coefficient,
      total_paid_ttc,
      cost_price_order,
      total_cost_allocations_ht: totalCostAllocationsHT,
      total_cost_allocations_ttc: totalCostAllocationsTTC
    };
  }

  // Nouvelle méthode pour calculer les coûts de revient par article
  calculateItemCostPrices(orderData, items) {
    const calculations = this.calculateAmounts(orderData);
    const { landed_cost_coefficient, total_cif_dzd } = calculations;

    if (!items || items.length === 0) {
      return [];
    }

    return items.map(item => {
      // Convertir le prix unitaire FOB en DZD
      const rate = this.getExchangeRate(orderData.fob_currency, orderData);
      const unit_fob_dzd = (item.unit_fob || 0) * rate;

      // Calculer le coût unitaire de revient
      // Coût Unitaire Revient = (Unit_FOB * Exchange Rate) * Landed Cost Coefficient
      const unit_cost_price = unit_fob_dzd * landed_cost_coefficient;

      // Calculer le coût total de revient pour cette ligne
      // Coût Total Revient Ligne = Coût Unitaire Revient * Quantity
      const total_cost_price = unit_cost_price * (item.quantity || 0);

      // Calculer le montant FOB en DZD pour cette ligne
      const amount_fob_dzd = unit_fob_dzd * (item.quantity || 0);

      return {
        ...item,
        unit_fob_dzd,
        unit_cost_price,
        total_cost_price,
        amount_fob_dzd,
        landed_cost_coefficient
      };
    });
  }

  // Méthode utilitaire pour obtenir le taux de change
  getExchangeRate(currency, orderData) {
    switch (currency?.toUpperCase()) {
      case 'USD': return orderData.exchange_rate_usd_dzd || 1;
      case 'EUR': return orderData.exchange_rate_eur_dzd || 1;
      case 'YUAN': case 'CNY': return orderData.exchange_rate_yuan_dzd || 1;
      default: return 1;
    }
  }
}

module.exports = OrderService;

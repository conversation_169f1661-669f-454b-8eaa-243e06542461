#!/bin/bash

echo "========================================"
echo "  Application de Gestion des Importations"
echo "========================================"
echo

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo "ERREUR: Node.js n'est pas installé"
    echo "Veuillez installer Node.js depuis https://nodejs.org/"
    exit 1
fi

echo "Node.js détecté: $(node --version)"

# Vérifier si les dépendances sont installées
if [ ! -d "node_modules" ]; then
    echo "Installation des dépendances..."
    npm install
    if [ $? -ne 0 ]; then
        echo "ERREUR: Échec de l'installation des dépendances"
        exit 1
    fi
fi

echo
echo "Démarrage de l'application..."
echo "Utilisateur par défaut: admin / admin123"
echo

# Démarrer l'application
npm start

if [ $? -ne 0 ]; then
    echo
    echo "ERREUR: L'application a rencontré un problème"
    read -p "Appuyez sur Entrée pour continuer..."
fi

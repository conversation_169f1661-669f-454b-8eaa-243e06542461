#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Couleurs pour la console
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorLog(color, message) {
    console.log(colors[color] + message + colors.reset);
}

function showHeader() {
    console.clear();
    colorLog('cyan', '========================================');
    colorLog('cyan', '  APPLICATION DE GESTION DES IMPORTATIONS');
    colorLog('cyan', '========================================');
    console.log('');
}

function checkPrerequisites() {
    return new Promise((resolve, reject) => {
        colorLog('yellow', '🔍 Vérification des prérequis...');
        
        // Vérifier Node.js
        exec('node --version', (error, stdout) => {
            if (error) {
                colorLog('red', '❌ ERREUR: Node.js n\'est pas installé ou n\'est pas dans le PATH');
                console.log('');
                colorLog('yellow', 'Veuillez installer Node.js depuis: https://nodejs.org/');
                reject(new Error('Node.js non trouvé'));
                return;
            }
            
            colorLog('green', `✅ Node.js installé : ${stdout.trim()}`);
            
            // Vérifier npm
            exec('npm --version', (error, stdout) => {
                if (error) {
                    colorLog('red', '❌ ERREUR: npm n\'est pas installé');
                    reject(new Error('npm non trouvé'));
                    return;
                }
                
                colorLog('green', `✅ npm installé : ${stdout.trim()}`);
                resolve();
            });
        });
    });
}

function checkPackageJson() {
    if (!fs.existsSync('package.json')) {
        colorLog('red', '❌ ERREUR: package.json non trouvé');
        colorLog('yellow', 'Assurez-vous que le script est dans le répertoire de l\'application');
        throw new Error('package.json non trouvé');
    }
}

function installDependencies() {
    return new Promise((resolve, reject) => {
        if (fs.existsSync('node_modules')) {
            resolve();
            return;
        }
        
        colorLog('yellow', '📦 Installation des dépendances...');
        console.log('');
        
        const npmInstall = spawn('npm', ['install'], {
            stdio: 'inherit',
            shell: true
        });
        
        npmInstall.on('close', (code) => {
            if (code === 0) {
                console.log('');
                colorLog('green', '✅ Dépendances installées avec succès');
                resolve();
            } else {
                colorLog('red', '❌ ERREUR: Échec de l\'installation des dépendances');
                reject(new Error('Installation échouée'));
            }
        });
        
        npmInstall.on('error', (error) => {
            colorLog('red', `❌ ERREUR: ${error.message}`);
            reject(error);
        });
    });
}

function createDataDirectory() {
    if (!fs.existsSync('data')) {
        colorLog('yellow', '📁 Création du répertoire data...');
        fs.mkdirSync('data');
    }
}

function showConnectionInfo() {
    console.log('');
    colorLog('cyan', '┌─────────────────────────────────────────┐');
    colorLog('cyan', '│  APPLICATION DE GESTION DES IMPORTATIONS │');
    colorLog('cyan', '├─────────────────────────────────────────┤');
    colorLog('cyan', '│  👤 Utilisateur : admin                 │');
    colorLog('cyan', '│  🔑 Mot de passe : admin123             │');
    colorLog('cyan', '└─────────────────────────────────────────┘');
    console.log('');
    colorLog('yellow', '⚠️  Les erreurs GPU sont normales et n\'affectent pas le fonctionnement');
    console.log('');
}

function launchApplication() {
    return new Promise((resolve, reject) => {
        colorLog('green', '🚀 Démarrage de l\'application Electron...');
        console.log('');
        
        const electronApp = spawn('npm', ['run', 'dev'], {
            stdio: 'inherit',
            shell: true
        });
        
        electronApp.on('close', (code) => {
            console.log('');
            colorLog('cyan', '📱 Application fermée');
            resolve(code);
        });
        
        electronApp.on('error', (error) => {
            colorLog('red', `❌ ERREUR lors du lancement: ${error.message}`);
            reject(error);
        });
        
        // Gérer Ctrl+C
        process.on('SIGINT', () => {
            colorLog('yellow', '\n🛑 Arrêt de l\'application...');
            electronApp.kill('SIGINT');
        });
    });
}

async function main() {
    try {
        showHeader();
        
        await checkPrerequisites();
        checkPackageJson();
        await installDependencies();
        createDataDirectory();
        
        console.log('');
        colorLog('cyan', '🔧 Vérification de la base de données...');
        
        showConnectionInfo();
        
        await launchApplication();
        
    } catch (error) {
        console.log('');
        colorLog('red', `❌ ERREUR: ${error.message}`);
        console.log('');
        
        // Attendre une entrée utilisateur avant de quitter
        process.stdin.setRawMode(true);
        process.stdin.resume();
        colorLog('yellow', 'Appuyez sur une touche pour quitter...');
        process.stdin.on('data', () => {
            process.exit(1);
        });
    }
}

// Lancer le script principal
main();

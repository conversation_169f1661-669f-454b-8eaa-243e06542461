class ReportService {
  constructor(database) {
    this.db = database;
  }

  // Rapport de suivi des commandes avec statuts et dates clés
  async getOrderTrackingReport(filters = {}) {
    try {
      const { dateFrom, dateTo, shipmentType, supplier } = filters;
      
      let whereClause = 'WHERE 1=1';
      const params = [];

      if (dateFrom) {
        whereClause += ' AND o.order_date >= ?';
        params.push(dateFrom);
      }

      if (dateTo) {
        whereClause += ' AND o.order_date <= ?';
        params.push(dateTo);
      }

      if (shipmentType) {
        whereClause += ' AND o.shipment_type = ?';
        params.push(shipmentType);
      }

      if (supplier) {
        whereClause += ' AND o.supplier_name LIKE ?';
        params.push(`%${supplier}%`);
      }

      const query = `
        SELECT 
          o.id,
          o.order_number,
          o.order_date,
          o.supplier_name,
          o.shipment_type,
          o.from_location,
          o.to_location,
          o.invoice_number,
          o.invoice_date,
          o.fob_amount_dzd,
          o.total_cif_dzd,
          o.landed_cost_ht,
          o.landed_cost_coefficient,
          o.total_paid_ttc,
          COUNT(oi.id) as items_count,
          SUM(oi.total_cost_price) as total_items_cost_price,
          an.actual_time_of_arrival,
          an.vessel_name,
          CASE 
            WHEN an.actual_time_of_arrival IS NOT NULL THEN 'Arrivé'
            WHEN o.invoice_date IS NOT NULL THEN 'En Transit'
            ELSE 'En Préparation'
          END as status
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN arrival_notices an ON o.id = an.order_id
        ${whereClause}
        GROUP BY o.id
        ORDER BY o.order_date DESC
      `;

      const orders = await this.db.all(query, params);
      return orders;
    } catch (error) {
      throw error;
    }
  }

  // Rapport de coûts par commande avec détails des allocations
  async getCostAnalysisReport(orderId = null) {
    try {
      let whereClause = '';
      const params = [];

      if (orderId) {
        whereClause = 'WHERE o.id = ?';
        params.push(orderId);
      }

      const query = `
        SELECT 
          o.id,
          o.order_number,
          o.order_date,
          o.supplier_name,
          o.fob_amount_currency,
          o.fob_currency,
          o.fob_amount_dzd,
          o.freight_amount_dzd,
          o.total_cif_dzd,
          o.landed_cost_ht,
          o.landed_cost_coefficient,
          o.total_paid_ttc,
          SUM(CASE WHEN ca.cost_type = 'CustomsDuties1' THEN ca.ht ELSE 0 END) as customs_duties_ht,
          SUM(CASE WHEN ca.cost_type = 'ImportDelivery' THEN ca.ht ELSE 0 END) as import_delivery_ht,
          SUM(CASE WHEN ca.cost_type = 'ShippingAgencyServices' THEN ca.ht ELSE 0 END) as shipping_agency_ht,
          SUM(CASE WHEN ca.cost_type = 'OtherMiscellaneous' THEN ca.ht ELSE 0 END) as other_costs_ht,
          SUM(ca.ht) as total_allocations_ht,
          SUM(ca.total_ttc) as total_allocations_ttc,
          COUNT(oi.id) as items_count,
          SUM(oi.total_cost_price) as total_items_cost_price
        FROM orders o
        LEFT JOIN cost_allocations ca ON o.id = ca.order_id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        ${whereClause}
        GROUP BY o.id
        ORDER BY o.order_date DESC
      `;

      const results = await this.db.all(query, params);
      return results;
    } catch (error) {
      throw error;
    }
  }

  // Rapport d'analyse des marges (si prix de vente disponible)
  async getMarginAnalysisReport() {
    try {
      // Pour l'instant, nous calculons les marges potentielles basées sur les coûts de revient
      const query = `
        SELECT 
          o.id,
          o.order_number,
          o.supplier_name,
          o.shipment_type,
          o.landed_cost_ht as total_cost,
          oi.part_number,
          oi.description,
          oi.quantity,
          oi.unit_cost_price,
          oi.total_cost_price,
          oi.unit_fob_dzd,
          (oi.unit_cost_price - oi.unit_fob_dzd) as unit_additional_cost,
          ((oi.unit_cost_price - oi.unit_fob_dzd) / oi.unit_fob_dzd * 100) as cost_increase_percentage
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        WHERE oi.unit_fob_dzd > 0
        ORDER BY cost_increase_percentage DESC
      `;

      const results = await this.db.all(query);
      return results;
    } catch (error) {
      throw error;
    }
  }

  // Historique des taux de change utilisés
  async getExchangeRateHistory(currency = null, dateFrom = null, dateTo = null) {
    try {
      let whereClause = 'WHERE 1=1';
      const params = [];

      if (dateFrom) {
        whereClause += ' AND order_date >= ?';
        params.push(dateFrom);
      }

      if (dateTo) {
        whereClause += ' AND order_date <= ?';
        params.push(dateTo);
      }

      if (currency) {
        whereClause += ' AND fob_currency = ?';
        params.push(currency);
      }

      const query = `
        SELECT 
          order_date,
          fob_currency,
          exchange_rate_usd_dzd,
          exchange_rate_eur_dzd,
          exchange_rate_yuan_dzd,
          COUNT(*) as orders_count,
          AVG(fob_amount_dzd) as avg_fob_amount_dzd
        FROM orders
        ${whereClause}
        GROUP BY order_date, fob_currency, exchange_rate_usd_dzd, exchange_rate_eur_dzd, exchange_rate_yuan_dzd
        ORDER BY order_date DESC
      `;

      const results = await this.db.all(query, params);
      return results;
    } catch (error) {
      throw error;
    }
  }

  // Analyse des coûts par type d'expédition
  async getCostsByShipmentType() {
    try {
      const query = `
        SELECT 
          shipment_type,
          COUNT(*) as orders_count,
          AVG(fob_amount_dzd) as avg_fob_amount,
          AVG(freight_amount_dzd) as avg_freight_amount,
          AVG(total_cif_dzd) as avg_cif_amount,
          AVG(landed_cost_ht) as avg_landed_cost,
          AVG(landed_cost_coefficient) as avg_cost_coefficient,
          SUM(total_cif_dzd) as total_cif_value,
          SUM(landed_cost_ht) as total_landed_cost
        FROM orders
        WHERE shipment_type IS NOT NULL
        GROUP BY shipment_type
        ORDER BY orders_count DESC
      `;

      const results = await this.db.all(query);
      return results;
    } catch (error) {
      throw error;
    }
  }

  // Top fournisseurs par volume et valeur
  async getTopSuppliers(limit = 10) {
    try {
      const query = `
        SELECT 
          supplier_name,
          COUNT(*) as orders_count,
          SUM(total_cif_dzd) as total_value,
          AVG(total_cif_dzd) as avg_order_value,
          SUM(landed_cost_ht) as total_landed_cost,
          AVG(landed_cost_coefficient) as avg_cost_coefficient,
          MIN(order_date) as first_order_date,
          MAX(order_date) as last_order_date
        FROM orders
        WHERE supplier_name IS NOT NULL AND supplier_name != ''
        GROUP BY supplier_name
        ORDER BY total_value DESC
        LIMIT ?
      `;

      const results = await this.db.all(query, [limit]);
      return results;
    } catch (error) {
      throw error;
    }
  }

  // Évolution des coûts par période (mensuelle)
  async getCostEvolutionByPeriod(period = 'month') {
    try {
      let dateFormat;
      switch (period) {
        case 'year':
          dateFormat = '%Y';
          break;
        case 'quarter':
          dateFormat = '%Y-Q' || "CASE WHEN CAST(strftime('%m', order_date) AS INTEGER) <= 3 THEN '1' WHEN CAST(strftime('%m', order_date) AS INTEGER) <= 6 THEN '2' WHEN CAST(strftime('%m', order_date) AS INTEGER) <= 9 THEN '3' ELSE '4' END";
          break;
        default: // month
          dateFormat = '%Y-%m';
      }

      const query = `
        SELECT 
          strftime('${dateFormat}', order_date) as period,
          COUNT(*) as orders_count,
          SUM(fob_amount_dzd) as total_fob,
          SUM(freight_amount_dzd) as total_freight,
          SUM(total_cif_dzd) as total_cif,
          SUM(landed_cost_ht) as total_landed_cost,
          AVG(landed_cost_coefficient) as avg_cost_coefficient,
          AVG(exchange_rate_usd_dzd) as avg_usd_rate,
          AVG(exchange_rate_eur_dzd) as avg_eur_rate
        FROM orders
        WHERE order_date IS NOT NULL
        GROUP BY strftime('${dateFormat}', order_date)
        ORDER BY period DESC
      `;

      const results = await this.db.all(query);
      return results;
    } catch (error) {
      throw error;
    }
  }

  // Analyse détaillée des allocations de coûts
  async getCostAllocationsAnalysis() {
    try {
      const query = `
        SELECT 
          ca.cost_type,
          ca.cost_name,
          COUNT(*) as frequency,
          SUM(ca.ht) as total_ht,
          SUM(ca.total_ttc) as total_ttc,
          AVG(ca.ht) as avg_ht,
          AVG(ca.total_ttc) as avg_ttc,
          MIN(ca.ht) as min_ht,
          MAX(ca.ht) as max_ht,
          (SUM(ca.ht) * 100.0 / (SELECT SUM(ht) FROM cost_allocations)) as percentage_of_total
        FROM cost_allocations ca
        WHERE ca.ht > 0
        GROUP BY ca.cost_type, ca.cost_name
        ORDER BY total_ht DESC
      `;

      const results = await this.db.all(query);
      return results;
    } catch (error) {
      throw error;
    }
  }

  // Rapport de performance des articles (les plus coûteux, les plus fréquents)
  async getItemsPerformanceReport() {
    try {
      const query = `
        SELECT 
          oi.part_number,
          oi.description,
          COUNT(*) as frequency,
          SUM(oi.quantity) as total_quantity,
          AVG(oi.unit_fob) as avg_unit_fob,
          AVG(oi.unit_cost_price) as avg_unit_cost_price,
          SUM(oi.total_cost_price) as total_cost_price,
          AVG(oi.unit_cost_price - oi.unit_fob_dzd) as avg_additional_cost_per_unit,
          COUNT(DISTINCT oi.order_id) as orders_count,
          GROUP_CONCAT(DISTINCT o.supplier_name) as suppliers
        FROM order_items oi
        JOIN orders o ON oi.order_id = o.id
        WHERE oi.part_number IS NOT NULL AND oi.part_number != ''
        GROUP BY oi.part_number, oi.description
        ORDER BY total_cost_price DESC
        LIMIT 50
      `;

      const results = await this.db.all(query);
      return results;
    } catch (error) {
      throw error;
    }
  }

  // Génération de données pour graphiques (Chart.js)
  async getChartData(chartType) {
    try {
      switch (chartType) {
        case 'costsByShipmentType':
          return await this.getCostsByShipmentType();
        
        case 'monthlyEvolution':
          return await this.getCostEvolutionByPeriod('month');
        
        case 'topSuppliers':
          return await this.getTopSuppliers(5);
        
        case 'costAllocations':
          return await this.getCostAllocationsAnalysis();
        
        default:
          throw new Error('Type de graphique non supporté');
      }
    } catch (error) {
      throw error;
    }
  }
}

module.exports = ReportService;

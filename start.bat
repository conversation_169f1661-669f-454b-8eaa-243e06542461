@echo off
echo ========================================
echo   Application de Gestion des Importations
echo ========================================
echo.

REM Vérifier si Node.js est installé
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Node.js n'est pas installé ou n'est pas dans le PATH
    echo Veuillez installer Node.js depuis https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js détecté: 
node --version

REM Vérifier si les dépendances sont installées
if not exist "node_modules" (
    echo Installation des dépendances...
    npm install
    if %errorlevel% neq 0 (
        echo ERREUR: Échec de l'installation des dépendances
        pause
        exit /b 1
    )
)

echo.
echo Démarrage de l'application...
echo Utilisateur par défaut: admin / admin123
echo.

REM Démarrer l'application
npm start

if %errorlevel% neq 0 (
    echo.
    echo ERREUR: L'application a rencontré un problème
    pause
)

{"name": "import-management-app", "version": "1.0.0", "description": "Application de gestion des importations avec Electron et SQLite", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps", "create-template": "node create-template.js"}, "keywords": ["electron", "sqlite", "import", "management", "desktop"], "author": "Votre Nom", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"sqlite3": "^5.1.6", "bcrypt": "^5.1.1", "xlsx": "^0.18.5", "moment": "^2.29.4"}, "build": {"appId": "com.yourcompany.import-management", "productName": "Import Management", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "src/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}
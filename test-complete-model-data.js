// Script pour insérer des données de test conformes au modèle complet obligatoire
const Database = require('./src/database/database');
const OrderService = require('./src/services/orderService');

async function insertCompleteModelData() {
    console.log('🚀 Insertion des données conformes au modèle complet obligatoire...');
    
    const database = new Database();
    await database.initialize();
    
    const orderService = new OrderService(database);

    // Données de test conformes au modèle exact fourni
    const orderNumber = 'AZEDFRTY' + Date.now();
    const testOrders = [
        {
            // Commande 1 - Modèle exact avec format #PART_NUMBER#
            order_number: orderNumber,
            order_date: '2024-01-15',
            supplier_name: 'Shanghai Auto Parts Manufacturing Co.',
            shipment_type: 'SEA',
            invoice_number: 'INV-SHA-2024-001',
            invoice_date: '2024-01-10',
            from_location: 'Shanghai Port',
            to_location: 'Port d\'Alger',
            operation_type: 'Spare Parts (for maintenance or specific resale)',
            goods_description: 'SPARE PARTS FOR VEHICLES',
            bank_name: 'Banque Nationale d\'Algérie',
            lc_number: 'LC-BNA-2024-001',
            lc_validation_date: '2024-01-05',
            payment_term: '30 jours après B/L',
            price_term: 'FOB Shanghai',
            quantity_pcs: 28,
            num_containers_20ft: 1,
            num_containers_40ft: 0,
            num_packages: 5,
            exchange_rate_usd_dzd: 134.50000, // Format français 5 chiffres
            exchange_rate_eur_dzd: 145.20000,
            exchange_rate_yuan_dzd: 19.80000,
            fob_amount_currency: 865.27,
            fob_currency: 'USD',
            freight_amount_currency: 150.00,
            total_cif_currency: 1015.27,
            
            // Articles conformes au modèle exact ITEM/order_number/#PART_NUMBER#/DESCRIPTION/Qty/U_FOB/AMOUNT
            items: [
                {
                    item_number: 1,
                    order_number: orderNumber,
                    part_number: '#**********#',
                    description: 'HOSE-HEATER INLET',
                    quantity: 10,
                    unit_fob: 2.11,
                    amount: 21.10
                },
                {
                    item_number: 2,
                    order_number: orderNumber,
                    part_number: '#5022072200#',
                    description: 'FRT WINDSHIELD ASSY,',
                    quantity: 8,
                    unit_fob: 99.13,
                    amount: 793.04
                },
                {
                    item_number: 3,
                    order_number: orderNumber,
                    part_number: '#4114870644#',
                    description: 'Oil filling valve of transmission',
                    quantity: 10,
                    unit_fob: 5.12,
                    amount: 51.20
                }
            ],
            
            // Avis d'arrivée complet obligatoire
            arrivalNotices: [
                {
                    voyage_number: 'MSC-2024-001', // "CALL AT PORT" (ESCALE) N°
                    bill_of_lading_number: 'MSCU-SHA-ALG-240115', // Bill of Lading (B/L) (Connaissement) N°
                    vessel_name: 'MSC Mediterranea', // Vessel Name (Navire)
                    shipowner: 'Mediterranean Shipping Company', // Shipowner (Armateur)
                    actual_time_of_arrival: '2024-02-20' // Actual Time of Arrival (Date Accostage)
                }
            ],
            
            // Allocations de coûts détaillées selon le modèle complet
            costAllocations: [
                // CUSTOMS DUTIES - QUITTANCE 1
                {
                    cost_category: 'CUSTOMS_DUTIES',
                    cost_type: 'CUSTOMS_DUTIES_1',
                    cost_name: 'ALGERIA CUSTOMS',
                    invoice_number: 'D3-ALG-001',
                    invoice_date: '2024-02-21',
                    d3_number: 'D3-2024-001',
                    d3_date: '2024-02-21',
                    quittance_number: 1,
                    total_ttc: 85000.00,
                    tva: 14407.00,
                    ht: 70593.00,
                    is_manual_entry: false
                },
                // CUSTOMS DUTIES - QUITTANCE 2
                {
                    cost_category: 'CUSTOMS_DUTIES',
                    cost_type: 'CUSTOMS_DUTIES_2',
                    cost_name: 'ALGERIA CUSTOMS',
                    invoice_number: 'D3-ALG-001-2',
                    invoice_date: '2024-02-21',
                    d3_number: 'D3-2024-001',
                    d3_date: '2024-02-21',
                    quittance_number: 2,
                    total_ttc: 25000.00,
                    tva: 4237.00,
                    ht: 20763.00,
                    is_manual_entry: false
                },
                // PORT FEES - IMPORT DELIVERY
                {
                    cost_category: 'PORT_FEES',
                    cost_type: 'IMPORT_DELIVERY',
                    cost_name: 'IMPORT DELIVERY',
                    invoice_number: 'DEL-ALG-001',
                    invoice_date: '2024-02-22',
                    total_ttc: 45000.00,
                    tva: 7627.00,
                    ht: 37373.00,
                    is_manual_entry: false
                },
                // PORT FEES - CUSTOMS INSPECTION
                {
                    cost_category: 'PORT_FEES',
                    cost_type: 'CUSTOMS_INSPECTION',
                    cost_name: 'CUSTOMS INSPECTION',
                    invoice_number: 'INSP-ALG-001',
                    invoice_date: '2024-02-22',
                    total_ttc: 15000.00,
                    tva: 2542.00,
                    ht: 12458.00,
                    is_manual_entry: false
                },
                // SHIPPING COMPANY FEES - SHIPPING AGENCY SERVICES
                {
                    cost_category: 'SHIPPING_COMPANY_FEES',
                    cost_type: 'SHIPPING_AGENCY_SERVICES',
                    cost_name: 'SHIPPING AGENCY SERVICES',
                    invoice_number: 'SHIP-ALG-001',
                    invoice_date: '2024-02-21',
                    total_ttc: 35000.00,
                    tva: 5932.00,
                    ht: 29068.00,
                    is_manual_entry: false
                },
                // SHIPPING COMPANY FEES - EMPTY CONTAINERS RETURN
                {
                    cost_category: 'SHIPPING_COMPANY_FEES',
                    cost_type: 'EMPTY_CONTAINERS_RETURN',
                    cost_name: 'EMPTY CONTAINERS RETURN',
                    invoice_number: 'CONT-ALG-001',
                    invoice_date: '2024-02-23',
                    total_ttc: 20000.00,
                    tva: 3390.00,
                    ht: 16610.00,
                    is_manual_entry: false
                },
                // SHIPPING COMPANY FEES - DEMURRAGE (saisie manuelle)
                {
                    cost_category: 'SHIPPING_COMPANY_FEES',
                    cost_type: 'DEMURRAGE',
                    cost_name: 'DEMURRAGE IF PRESENT',
                    invoice_number: 'DEM-ALG-001',
                    invoice_date: '2024-02-23',
                    total_ttc: 0.00,
                    tva: 0.00,
                    ht: 15000.00, // Saisie manuelle
                    is_manual_entry: true
                },
                // OTHER MISCELLANEOUS EXPENSES
                {
                    cost_category: 'OTHER_MISCELLANEOUS_EXPENSES',
                    cost_type: 'OTHER_MISCELLANEOUS',
                    cost_name: 'OTHER MISCELLANEOUS EXPENSES',
                    invoice_number: 'MISC-ALG-001',
                    invoice_date: '2024-02-24',
                    total_ttc: 12000.00,
                    tva: 2034.00,
                    ht: 9966.00,
                    is_manual_entry: false
                },
                // TRANSIT SERVICES EXPENSES
                {
                    cost_category: 'TRANSIT_SERVICES_EXPENSES',
                    cost_type: 'TRANSIT_SERVICES',
                    cost_name: 'TRANSIT SERVICES EXPENSES',
                    invoice_number: 'TRANS-ALG-001',
                    invoice_date: '2024-02-25',
                    total_ttc: 18000.00,
                    tva: 3051.00,
                    ht: 14949.00,
                    is_manual_entry: false
                }
            ]
        }
    ];

    // Insérer les commandes de test
    for (const orderData of testOrders) {
        try {
            console.log(`📦 Création de la commande ${orderData.order_number}...`);
            const result = await orderService.createOrder(orderData);
            console.log(`✅ Commande ${orderData.order_number} créée avec succès (ID: ${result.id})`);
            
            // Afficher les calculs selon le modèle
            console.log('\n💰 Calculs automatiques conformes au modèle :');
            console.log(`   FOB AMOUNT DZD: ${result.fob_amount_dzd?.toLocaleString('fr-FR', {minimumFractionDigits: 5})} DZD`);
            console.log(`   FREIGHT DZD: ${result.freight_amount_dzd?.toLocaleString('fr-FR', {minimumFractionDigits: 5})} DZD`);
            console.log(`   TOTAL AMOUNT CIF DZD: ${result.total_cif_dzd?.toLocaleString('fr-FR', {minimumFractionDigits: 5})} DZD`);
            
            console.log('\n📊 Totaux par catégorie :');
            console.log(`   Customs Duties Overall Totals HT: ${result.customs_duties_total_ht?.toLocaleString('fr-FR')} DZD`);
            console.log(`   Port Fees Overall Totals HT: ${result.port_fees_total_ht?.toLocaleString('fr-FR')} DZD`);
            console.log(`   Shipping Company Fees Overall Totals HT: ${result.shipping_company_fees_total_ht?.toLocaleString('fr-FR')} DZD`);
            console.log(`   Other Miscellaneous Expenses HT: ${result.other_miscellaneous_expenses_total_ht?.toLocaleString('fr-FR')} DZD`);
            console.log(`   Transit Services Expenses HT: ${result.transit_services_expenses_total_ht?.toLocaleString('fr-FR')} DZD`);
            
            console.log('\n🏭 Calculs finaux :');
            console.log(`   Landed Cost HT: ${result.landed_cost_ht?.toLocaleString('fr-FR')} DZD`);
            console.log(`   Landed Cost Coefficient: ${result.landed_cost_coefficient?.toFixed(5)}`);
            console.log(`   Total Paid TTC: ${result.total_paid_ttc?.toLocaleString('fr-FR')} DZD`);
            
            console.log('\n📋 Articles avec format #PART_NUMBER# :');
            if (result.items && result.items.length > 0) {
                result.items.forEach(item => {
                    console.log(`   ${item.item_number}. ${item.part_number} - ${item.description}`);
                    console.log(`      Qty: ${item.quantity}, U_FOB: ${item.unit_fob}, Amount: ${item.amount}`);
                    console.log(`      Prix revient unitaire: ${item.unit_cost_price?.toFixed(5)} DZD`);
                });
            }
            console.log('');
            
        } catch (error) {
            console.error(`❌ Erreur lors de la création de ${orderData.order_number}:`, error.message);
        }
    }

    await database.close();
    console.log('🎉 Insertion des données conformes au modèle terminée !');
    console.log('');
    console.log('📊 Résumé des données insérées conformes au modèle obligatoire :');
    console.log('   ✅ Commande avec format exact AZEDFRTY2123');
    console.log('   ✅ 3 articles avec format #PART_NUMBER# obligatoire');
    console.log('   ✅ Avis d\'arrivée complet (voyage, B/L, navire, armateur, date)');
    console.log('   ✅ 9 allocations de coûts détaillées par catégorie');
    console.log('   ✅ Quittances douanes 1 et 2 avec D3');
    console.log('   ✅ Frais portuaires (delivery + inspection)');
    console.log('   ✅ Frais compagnie maritime (agency + containers + demurrage)');
    console.log('   ✅ Frais divers et services de transit');
    console.log('   ✅ Calculs automatiques avec format français (5 chiffres)');
    console.log('   ✅ Totaux par catégorie selon formules exactes');
    console.log('');
    console.log('🚀 Toutes les spécifications obligatoires sont respectées !');
}

// Exécuter le script
insertCompleteModelData().catch(console.error);

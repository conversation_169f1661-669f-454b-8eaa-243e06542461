-- Script de test pour insérer des données d'exemple
-- Exécuter ce script pour tester l'application avec des données de démonstration

-- Insérer des commandes d'exemple
INSERT INTO orders (
    order_number, order_date, supplier_name, shipment_type,
    invoice_number, invoice_date, from_location, to_location,
    operation_type, goods_description, bank_name, lc_number,
    lc_validation_date, payment_term, price_term, quantity_pcs,
    num_containers_20ft, num_containers_40ft, num_packages,
    exchange_rate_usd_dzd, exchange_rate_eur_dzd, exchange_rate_yuan_dzd,
    fob_amount_currency, fob_currency, freight_amount_currency,
    total_cif_currency, fob_amount_dzd, freight_amount_dzd,
    total_cif_dzd, landed_cost_ht, landed_cost_coefficient, total_paid_ttc
) VALUES 
(
    'CMD001', '2024-01-15', 'Shanghai Electronics Co.', 'SEA',
    'INV-2024-001', '2024-01-10', 'Shanghai', 'Alger',
    'Import', 'Composants électroniques', 'BNA', 'LC-2024-001',
    '2024-01-05', '30 jours', 'FOB Shanghai', 5000,
    2, 0, 100,
    134.50, 145.20, 19.80,
    50000.00, 'USD', 3000.00,
    53000.00, 6725000.00, 403500.00,
    7128500.00, 7128500.00, 1.06, 7128500.00
),
(
    'CMD002', '2024-01-20', 'German Machinery GmbH', 'AIR',
    'INV-2024-002', '2024-01-18', 'Hamburg', 'Alger',
    'Import', 'Machines industrielles', 'CPA', 'LC-2024-002',
    '2024-01-15', '60 jours', 'FOB Hamburg', 10,
    0, 0, 5,
    134.50, 145.20, 19.80,
    25000.00, 'EUR', 2000.00,
    27000.00, 3630000.00, 290400.00,
    3920400.00, 3920400.00, 1.08, 3920400.00
),
(
    'CMD003', '2024-02-01', 'Beijing Auto Parts Ltd.', 'Express',
    'INV-2024-003', '2024-01-28', 'Beijing', 'Oran',
    'Import', 'Pièces automobiles', 'BADR', 'LC-2024-003',
    '2024-01-25', '45 jours', 'FOB Beijing', 2000,
    0, 1, 50,
    134.50, 145.20, 19.80,
    15000.00, 'CNY', 1500.00,
    16500.00, 297000.00, 29700.00,
    326700.00, 326700.00, 1.10, 326700.00
);

-- Insérer des articles pour les commandes
INSERT INTO order_items (order_id, item_number, part_number, description, quantity, unit_fob, amount) VALUES
-- Articles pour CMD001
(1, 1, 'ELEC-001', 'Processeur ARM Cortex', 1000, 25.00, 25000.00),
(1, 2, 'ELEC-002', 'Mémoire DDR4 8GB', 500, 50.00, 25000.00),

-- Articles pour CMD002
(2, 1, 'MACH-001', 'Moteur électrique 5kW', 5, 3000.00, 15000.00),
(2, 2, 'MACH-002', 'Variateur de vitesse', 5, 2000.00, 10000.00),

-- Articles pour CMD003
(3, 1, 'AUTO-001', 'Filtre à huile', 1000, 8.00, 8000.00),
(3, 2, 'AUTO-002', 'Plaquettes de frein', 500, 14.00, 7000.00);

-- Insérer des avis d'arrivée
INSERT INTO arrival_notices (order_id, voyage_number, bill_of_lading_number, vessel_name, shipowner, actual_time_of_arrival) VALUES
(1, 'V2024-001', 'BL-SHA-ALG-001', 'MSC Mediterranea', 'MSC', '2024-02-15'),
(2, 'LH-2024-002', 'AWB-HAM-ALG-002', 'Lufthansa Cargo', 'Lufthansa', '2024-01-22'),
(3, 'DHL-2024-003', 'EXP-BEJ-ORA-003', 'DHL Express', 'DHL', '2024-02-03');

-- Insérer des allocations de coûts
INSERT INTO cost_allocations (order_id, cost_type, cost_name, invoice_number, invoice_date, total_ttc, tva, ht, is_manual_entry) VALUES
-- Coûts pour CMD001
(1, 'CustomsDuties1', 'ALGERIA CUSTOMS', 'CUST-001', '2024-02-16', 500000.00, 85000.00, 415000.00, FALSE),
(1, 'ImportDelivery', 'IMPORT DELIVERY', 'DEL-001', '2024-02-17', 120000.00, 20000.00, 100000.00, FALSE),
(1, 'ShippingAgencyServices', 'SHIPPING AGENCY', 'SHIP-001', '2024-02-16', 60000.00, 10000.00, 50000.00, FALSE),

-- Coûts pour CMD002
(2, 'CustomsDuties1', 'ALGERIA CUSTOMS', 'CUST-002', '2024-01-23', 300000.00, 51000.00, 249000.00, FALSE),
(2, 'ImportDelivery', 'IMPORT DELIVERY', 'DEL-002', '2024-01-24', 80000.00, 13600.00, 66400.00, FALSE),

-- Coûts pour CMD003
(3, 'CustomsDuties1', 'ALGERIA CUSTOMS', 'CUST-003', '2024-02-04', 50000.00, 8500.00, 41500.00, FALSE),
(3, 'OtherMiscellaneous', 'DEMURRAGE HT', 'DEM-003', '2024-02-05', 25000.00, 0.00, 25000.00, TRUE);

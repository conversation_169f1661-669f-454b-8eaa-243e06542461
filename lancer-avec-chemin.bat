@echo off
title Application de Gestion des Importations - Lancement avec chemins
color 0A

echo.
echo ========================================
echo   APPLICATION DE GESTION DES IMPORTATIONS
echo ========================================
echo   Lancement avec détection automatique
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 Recherche de Node.js...
echo.

REM Chemins communs d'installation de Node.js
set "NODE_PATHS="C:\Program Files\nodejs" "C:\Program Files (x86)\nodejs" "%APPDATA%\npm" "%LOCALAPPDATA%\Programs\nodejs""

REM Tester les chemins communs
for %%p in (%NODE_PATHS%) do (
    if exist "%%~p\node.exe" (
        echo ✅ Node.js trouvé dans : %%~p
        set "NODE_PATH=%%~p"
        goto :found_node
    )
)

REM Si Node.js n'est pas trouvé dans les chemins communs
echo ❌ Node.js non trouvé dans les emplacements standards
echo.
echo 📥 Installation requise...
echo.
echo Lancement du script d'installation...
call install-nodejs.bat
goto :end

:found_node
echo.
echo 🔧 Configuration de l'environnement...

REM Ajouter Node.js au PATH temporairement
set "PATH=%NODE_PATH%;%PATH%"

REM Vérifier que Node.js fonctionne
"%NODE_PATH%\node.exe" --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Erreur lors de l'exécution de Node.js
    goto :error
)

echo ✅ Node.js opérationnel
"%NODE_PATH%\node.exe" --version

REM Vérifier npm
"%NODE_PATH%\npm.cmd" --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm non disponible
    goto :error
)

echo ✅ npm opérationnel
"%NODE_PATH%\npm.cmd" --version

echo.
echo 📦 Vérification des dépendances...

REM Installer les dépendances si nécessaire
if not exist "node_modules" (
    echo.
    echo 📥 Installation des dépendances...
    "%NODE_PATH%\npm.cmd" install
    if %errorlevel% neq 0 (
        echo ❌ Erreur lors de l'installation des dépendances
        goto :error
    )
    echo ✅ Dépendances installées
) else (
    echo ✅ Dépendances déjà présentes
)

REM Créer le répertoire data
if not exist "data" (
    echo 📁 Création du répertoire data...
    mkdir data
)

echo.
echo ========================================
echo   LANCEMENT DE L'APPLICATION
echo ========================================
echo.
echo 👤 Utilisateur : admin
echo 🔑 Mot de passe : admin123
echo.
echo ⚠️  Les erreurs GPU sont normales
echo.
echo 🚀 Démarrage de l'application...
echo.

REM Lancer l'application
"%NODE_PATH%\npm.cmd" run dev

goto :end

:error
echo.
echo ========================================
echo   ERREUR
echo ========================================
echo.
echo ❌ Impossible de lancer l'application
echo.
echo Solutions :
echo 1. Installez Node.js depuis https://nodejs.org/
echo 2. Redémarrez votre ordinateur après installation
echo 3. Relancez ce script
echo.
pause
exit /b 1

:end
echo.
echo ========================================
echo   APPLICATION FERMÉE
echo ========================================
echo.
pause

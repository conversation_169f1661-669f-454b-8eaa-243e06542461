@echo off
echo ========================================
echo   APPLICATION DE GESTION DES IMPORTATIONS
echo ========================================
echo.
echo 🚀 Lancement de l'application...
echo.

REM Vérifier si Node.js est installé
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERREUR: Node.js n'est pas installé ou n'est pas dans le PATH
    echo.
    echo Veuillez installer Node.js depuis: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Vérifier si npm est installé
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERREUR: npm n'est pas installé
    echo.
    pause
    exit /b 1
)

REM Aller dans le répertoire de l'application
cd /d "%~dp0"

REM Vérifier si package.json existe
if not exist "package.json" (
    echo ❌ ERREUR: package.json non trouvé
    echo Assurez-vous que le script est dans le répertoire de l'application
    echo.
    pause
    exit /b 1
)

REM Vérifier si node_modules existe, sinon installer les dépendances
if not exist "node_modules" (
    echo 📦 Installation des dépendances...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ ERREUR: Échec de l'installation des dépendances
        echo.
        pause
        exit /b 1
    )
    echo.
    echo ✅ Dépendances installées avec succès
    echo.
)

REM Créer le répertoire data s'il n'existe pas
if not exist "data" (
    echo 📁 Création du répertoire data...
    mkdir data
)

echo 🔧 Vérification de la base de données...
echo.

REM Lancer l'application
echo 🚀 Démarrage de l'application Electron...
echo.
echo ┌─────────────────────────────────────────┐
echo │  APPLICATION DE GESTION DES IMPORTATIONS │
echo ├─────────────────────────────────────────┤
echo │  👤 Utilisateur : admin                 │
echo │  🔑 Mot de passe : admin123             │
echo └─────────────────────────────────────────┘
echo.
echo ⚠️  Les erreurs GPU sont normales et n'affectent pas le fonctionnement
echo.

npm run dev

REM Si l'application se ferme, afficher un message
echo.
echo 📱 Application fermée
echo.
pause

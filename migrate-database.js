// Script de migration pour ajouter les nouvelles colonnes à la base de données
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

async function migrateDatabase() {
    console.log('🔄 Migration de la base de données...');
    
    const dbPath = path.join(__dirname, 'data/import_management.db');
    
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(dbPath, (err) => {
            if (err) {
                console.error('❌ Erreur lors de l\'ouverture de la base de données:', err);
                reject(err);
                return;
            }
            
            console.log('✅ Base de données ouverte');
            
            // Ajouter les nouvelles colonnes à la table order_items
            const migrations = [
                // Migration 1: Ajouter les colonnes de coût de revient à order_items
                `ALTER TABLE order_items ADD COLUMN unit_fob_dzd REAL DEFAULT 0`,
                `ALTER TABLE order_items ADD COLUMN unit_cost_price REAL DEFAULT 0`,
                `ALTER TABLE order_items ADD COLUMN total_cost_price REAL DEFAULT 0`,
                `ALTER TABLE order_items ADD COLUMN amount_fob_dzd REAL DEFAULT 0`,
                
                // Migration 2: Ajouter la colonne exchange_rate_yuan_dzd à orders si elle n'existe pas
                `ALTER TABLE orders ADD COLUMN exchange_rate_yuan_dzd REAL DEFAULT 0`
            ];
            
            let completed = 0;
            const total = migrations.length;
            
            migrations.forEach((migration, index) => {
                db.run(migration, (err) => {
                    if (err && !err.message.includes('duplicate column name')) {
                        console.error(`❌ Erreur migration ${index + 1}:`, err.message);
                    } else {
                        console.log(`✅ Migration ${index + 1}/${total} terminée`);
                    }
                    
                    completed++;
                    if (completed === total) {
                        console.log('🎉 Toutes les migrations terminées !');
                        
                        db.close((err) => {
                            if (err) {
                                console.error('❌ Erreur lors de la fermeture:', err);
                                reject(err);
                            } else {
                                console.log('✅ Base de données fermée');
                                resolve();
                            }
                        });
                    }
                });
            });
        });
    });
}

// Exécuter la migration
migrateDatabase()
    .then(() => {
        console.log('');
        console.log('🚀 Migration terminée avec succès !');
        console.log('Vous pouvez maintenant exécuter: node test-advanced-data.js');
    })
    .catch((error) => {
        console.error('❌ Erreur lors de la migration:', error);
        process.exit(1);
    });

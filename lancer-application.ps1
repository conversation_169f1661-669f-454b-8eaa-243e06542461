# Script PowerShell pour lancer l'application de gestion des importations
param(
    [switch]$NoInstall,
    [switch]$Verbose
)

# Fonction pour afficher des messages colorés
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# En-tête
Clear-Host
Write-ColorOutput Cyan @"
========================================
  APPLICATION DE GESTION DES IMPORTATIONS
========================================
"@
Write-Host ""

# Vérifier les prérequis
Write-ColorOutput Yellow "🔍 Vérification des prérequis..."

# Vérifier Node.js
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-ColorOutput Green "✅ Node.js installé : $nodeVersion"
    } else {
        throw "Node.js non trouvé"
    }
} catch {
    Write-ColorOutput Red "❌ ERREUR: Node.js n'est pas installé ou n'est pas dans le PATH"
    Write-Host ""
    Write-ColorOutput Yellow "Veuillez installer Node.js depuis: https://nodejs.org/"
    Write-Host ""
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

# Vérifier npm
try {
    $npmVersion = npm --version 2>$null
    if ($npmVersion) {
        Write-ColorOutput Green "✅ npm installé : $npmVersion"
    } else {
        throw "npm non trouvé"
    }
} catch {
    Write-ColorOutput Red "❌ ERREUR: npm n'est pas installé"
    Write-Host ""
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

# Aller dans le répertoire du script
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

# Vérifier package.json
if (-not (Test-Path "package.json")) {
    Write-ColorOutput Red "❌ ERREUR: package.json non trouvé"
    Write-ColorOutput Yellow "Assurez-vous que le script est dans le répertoire de l'application"
    Write-Host ""
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

# Installer les dépendances si nécessaire
if (-not $NoInstall -and -not (Test-Path "node_modules")) {
    Write-ColorOutput Yellow "📦 Installation des dépendances..."
    Write-Host ""
    
    try {
        npm install
        Write-Host ""
        Write-ColorOutput Green "✅ Dépendances installées avec succès"
    } catch {
        Write-ColorOutput Red "❌ ERREUR: Échec de l'installation des dépendances"
        Write-Host ""
        Read-Host "Appuyez sur Entrée pour quitter"
        exit 1
    }
}

# Créer le répertoire data
if (-not (Test-Path "data")) {
    Write-ColorOutput Yellow "📁 Création du répertoire data..."
    New-Item -ItemType Directory -Name "data" | Out-Null
}

Write-Host ""
Write-ColorOutput Cyan "🔧 Vérification de la base de données..."
Write-Host ""

# Afficher les informations de connexion
Write-ColorOutput Cyan @"
┌─────────────────────────────────────────┐
│  APPLICATION DE GESTION DES IMPORTATIONS │
├─────────────────────────────────────────┤
│  👤 Utilisateur : admin                 │
│  🔑 Mot de passe : admin123             │
└─────────────────────────────────────────┘
"@
Write-Host ""
Write-ColorOutput Yellow "⚠️  Les erreurs GPU sont normales et n'affectent pas le fonctionnement"
Write-Host ""

# Lancer l'application
Write-ColorOutput Green "🚀 Démarrage de l'application Electron..."
Write-Host ""

try {
    if ($Verbose) {
        npm run dev
    } else {
        npm run dev 2>$null
    }
} catch {
    Write-ColorOutput Red "❌ ERREUR lors du lancement de l'application"
    Write-Host ""
} finally {
    Write-Host ""
    Write-ColorOutput Cyan "📱 Application fermée"
    Write-Host ""
    Read-Host "Appuyez sur Entrée pour quitter"
}

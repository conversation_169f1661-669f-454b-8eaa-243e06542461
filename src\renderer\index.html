<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Importations</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="app">
        <!-- Page de connexion -->
        <div id="loginPage" class="login-container">
            <div class="login-card">
                <div class="text-center mb-4">
                    <i class="bi bi-box-seam display-4 text-primary"></i>
                    <h2 class="mt-3">Gestion des Importations</h2>
                    <p class="text-muted">Connectez-vous pour accéder à l'application</p>
                </div>
                
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">Nom d'utilisateur</label>
                        <input type="text" class="form-control" id="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Mot de passe</label>
                        <input type="password" class="form-control" id="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-box-arrow-in-right me-2"></i>Se connecter
                    </button>
                </form>
                
                <div id="loginError" class="alert alert-danger mt-3 d-none"></div>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        Utilisateur par défaut: admin / admin123
                    </small>
                </div>
            </div>
        </div>

        <!-- Application principale -->
        <div id="mainApp" class="d-none">
            <!-- Navigation -->
            <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                <div class="container-fluid">
                    <a class="navbar-brand" href="#">
                        <i class="bi bi-box-seam me-2"></i>Gestion des Importations
                    </a>
                    
                    <div class="navbar-nav ms-auto">
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>
                                <span id="currentUser">Utilisateur</span>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" id="logoutBtn">
                                    <i class="bi bi-box-arrow-right me-2"></i>Déconnexion
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Sidebar et contenu principal -->
            <div class="container-fluid">
                <div class="row">
                    <!-- Sidebar -->
                    <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                        <div class="position-sticky pt-3">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link active" href="#" data-page="dashboard">
                                        <i class="bi bi-speedometer2 me-2"></i>Tableau de bord
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" data-page="orders">
                                        <i class="bi bi-list-ul me-2"></i>Commandes
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" data-page="new-order">
                                        <i class="bi bi-plus-circle me-2"></i>Nouvelle commande
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" data-page="reports">
                                        <i class="bi bi-graph-up me-2"></i>Rapports
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" data-page="import-export">
                                        <i class="bi bi-arrow-down-up me-2"></i>Import/Export
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </nav>

                    <!-- Contenu principal -->
                    <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                        <!-- Page Tableau de bord -->
                        <div id="dashboardPage" class="page-content">
                            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                                <h1 class="h2">Tableau de bord</h1>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card text-white bg-primary">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="totalOrders">0</h4>
                                                    <p class="card-text">Total Commandes</p>
                                                </div>
                                                <i class="bi bi-list-ul display-6"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-white bg-success">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="totalValue">0 DZD</h4>
                                                    <p class="card-text">Valeur Totale</p>
                                                </div>
                                                <i class="bi bi-currency-dollar display-6"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-white bg-info">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="thisMonthOrders">0</h4>
                                                    <p class="card-text">Ce Mois</p>
                                                </div>
                                                <i class="bi bi-calendar-month display-6"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-white bg-warning">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="avgValue">0 DZD</h4>
                                                    <p class="card-text">Valeur Moyenne</p>
                                                </div>
                                                <i class="bi bi-graph-up display-6"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5>Commandes Récentes</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>N° Commande</th>
                                                            <th>Date</th>
                                                            <th>Fournisseur</th>
                                                            <th>Type</th>
                                                            <th>Valeur CIF DZD</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="recentOrdersTable">
                                                        <!-- Les données seront chargées dynamiquement -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Page Liste des commandes -->
                        <div id="ordersPage" class="page-content d-none">
                            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                                <h1 class="h2">Gestion des Commandes</h1>
                                <div class="btn-toolbar mb-2 mb-md-0">
                                    <button type="button" class="btn btn-primary" data-page="new-order">
                                        <i class="bi bi-plus-circle me-2"></i>Nouvelle commande
                                    </button>
                                </div>
                            </div>

                            <!-- Filtres -->
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="searchOrders" placeholder="Rechercher...">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="filterShipmentType">
                                        <option value="">Tous les types</option>
                                        <option value="SEA">Maritime</option>
                                        <option value="AIR">Aérien</option>
                                        <option value="Express">Express</option>
                                        <option value="Other">Autre</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <input type="date" class="form-control" id="filterDateFrom" placeholder="Date début">
                                </div>
                                <div class="col-md-2">
                                    <input type="date" class="form-control" id="filterDateTo" placeholder="Date fin">
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-secondary" id="clearFilters">
                                        <i class="bi bi-x-circle me-2"></i>Effacer filtres
                                    </button>
                                </div>
                            </div>

                            <!-- Statistiques rapides -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="filteredOrdersCount">0</h4>
                                                    <p class="card-text">Commandes Affichées</p>
                                                </div>
                                                <i class="bi bi-list-ul display-6"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="filteredTotalValue">0 DZD</h4>
                                                    <p class="card-text">Valeur Totale</p>
                                                </div>
                                                <i class="bi bi-currency-dollar display-6"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="filteredAvgValue">0 DZD</h4>
                                                    <p class="card-text">Valeur Moyenne</p>
                                                </div>
                                                <i class="bi bi-graph-up display-6"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="filteredAvgCoeff">0.00</h4>
                                                    <p class="card-text">Coefficient Moyen</p>
                                                </div>
                                                <i class="bi bi-calculator display-6"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Tableau des commandes -->
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5>Liste des Commandes</h5>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshOrdersBtn">
                                            <i class="bi bi-arrow-clockwise me-1"></i>Actualiser
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success" id="exportOrdersBtn">
                                            <i class="bi bi-download me-1"></i>Exporter
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-primary" id="bulkActionsBtn">
                                            <i class="bi bi-check2-square me-1"></i>Actions groupées
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>
                                                        <input type="checkbox" class="form-check-input" id="selectAllOrders">
                                                    </th>
                                                    <th>
                                                        <a href="#" class="text-decoration-none" data-sort="order_number">
                                                            N° Commande <i class="bi bi-arrow-down-up"></i>
                                                        </a>
                                                    </th>
                                                    <th>
                                                        <a href="#" class="text-decoration-none" data-sort="order_date">
                                                            Date <i class="bi bi-arrow-down-up"></i>
                                                        </a>
                                                    </th>
                                                    <th>
                                                        <a href="#" class="text-decoration-none" data-sort="supplier_name">
                                                            Fournisseur <i class="bi bi-arrow-down-up"></i>
                                                        </a>
                                                    </th>
                                                    <th>Type Expédition</th>
                                                    <th>Origine → Destination</th>
                                                    <th>
                                                        <a href="#" class="text-decoration-none" data-sort="total_cif_dzd">
                                                            CIF DZD <i class="bi bi-arrow-down-up"></i>
                                                        </a>
                                                    </th>
                                                    <th>
                                                        <a href="#" class="text-decoration-none" data-sort="landed_cost_ht">
                                                            Coût Atterrissage <i class="bi bi-arrow-down-up"></i>
                                                        </a>
                                                    </th>
                                                    <th>Coefficient</th>
                                                    <th>Articles</th>
                                                    <th>Statut</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="ordersTable">
                                                <!-- Les données seront chargées dynamiquement -->
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Pagination -->
                                    <nav aria-label="Pagination des commandes">
                                        <ul class="pagination justify-content-center" id="ordersPagination">
                                            <!-- Pagination générée dynamiquement -->
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>

                        <!-- Page Nouvelle commande -->
                        <div id="newOrderPage" class="page-content d-none">
                            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                                <h1 class="h2" id="orderFormTitle">Nouvelle Commande</h1>
                                <div class="btn-toolbar mb-2 mb-md-0">
                                    <button type="button" class="btn btn-outline-secondary me-2" data-page="orders">
                                        <i class="bi bi-arrow-left me-2"></i>Retour
                                    </button>
                                </div>
                            </div>

                            <form id="orderForm">
                                <!-- Informations générales -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>Informations Générales</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="orderNumber" class="form-label">Numéro de Commande *</label>
                                                    <input type="text" class="form-control" id="orderNumber" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="orderDate" class="form-label">Date de Commande</label>
                                                    <input type="date" class="form-control" id="orderDate">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="supplierName" class="form-label">Nom du Fournisseur</label>
                                                    <input type="text" class="form-control" id="supplierName">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="shipmentType" class="form-label">Type d'Expédition</label>
                                                    <select class="form-select" id="shipmentType">
                                                        <option value="SEA">Maritime (SEA)</option>
                                                        <option value="AIR">Aérien (AIR)</option>
                                                        <option value="Express">Express</option>
                                                        <option value="Other">Autre</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="fromLocation" class="form-label">Origine</label>
                                                    <input type="text" class="form-control" id="fromLocation">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="toLocation" class="form-label">Destination</label>
                                                    <input type="text" class="form-control" id="toLocation">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    <label for="goodsDescription" class="form-label">Description des Marchandises</label>
                                                    <textarea class="form-control" id="goodsDescription" rows="3"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Informations financières -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>Informations Financières</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="fobAmountCurrency" class="form-label">Montant FOB</label>
                                                    <input type="number" step="0.01" class="form-control" id="fobAmountCurrency">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="fobCurrency" class="form-label">Devise FOB</label>
                                                    <select class="form-select" id="fobCurrency">
                                                        <option value="USD">USD</option>
                                                        <option value="EUR">EUR</option>
                                                        <option value="CNY">Yuan (CNY)</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="freightAmountCurrency" class="form-label">Montant Fret</label>
                                                    <input type="number" step="0.01" class="form-control" id="freightAmountCurrency">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="exchangeRateUsdDzd" class="form-label">Taux USD/DZD</label>
                                                    <input type="number" step="0.01" class="form-control" id="exchangeRateUsdDzd">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="exchangeRateEurDzd" class="form-label">Taux EUR/DZD</label>
                                                    <input type="number" step="0.01" class="form-control" id="exchangeRateEurDzd">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="exchangeRateYuanDzd" class="form-label">Taux Yuan/DZD</label>
                                                    <input type="number" step="0.01" class="form-control" id="exchangeRateYuanDzd">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Articles de la commande -->
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5>Articles de la Commande</h5>
                                        <button type="button" class="btn btn-sm btn-outline-primary" id="addItemBtn">
                                            <i class="bi bi-plus-circle me-1"></i>Ajouter Article
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm" id="itemsTable">
                                                <thead>
                                                    <tr>
                                                        <th>N°</th>
                                                        <th>Référence</th>
                                                        <th>Description</th>
                                                        <th>Quantité</th>
                                                        <th>Prix Unit. FOB</th>
                                                        <th>Montant</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="itemsTableBody">
                                                    <!-- Les articles seront ajoutés dynamiquement -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Avis d'arrivée -->
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5>Avis d'Arrivée</h5>
                                        <button type="button" class="btn btn-sm btn-outline-info" id="addArrivalNoticeBtn">
                                            <i class="bi bi-plus-circle me-1"></i>Ajouter Avis
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div id="arrivalNoticesContainer">
                                            <!-- Les avis d'arrivée seront ajoutés dynamiquement -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Allocations de coûts -->
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5>Allocations de Coûts</h5>
                                        <button type="button" class="btn btn-sm btn-outline-warning" id="addCostAllocationBtn">
                                            <i class="bi bi-plus-circle me-1"></i>Ajouter Coût
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div id="costAllocationsContainer">
                                            <!-- Les allocations de coûts seront ajoutées dynamiquement -->
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-md-6">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <h6>Résumé des Calculs</h6>
                                                        <div class="row">
                                                            <div class="col-6">
                                                                <small class="text-muted">Total CIF DZD:</small>
                                                                <div id="calculatedCifDzd" class="fw-bold">0.00 DZD</div>
                                                            </div>
                                                            <div class="col-6">
                                                                <small class="text-muted">Coût d'Atterrissage HT:</small>
                                                                <div id="calculatedLandedCost" class="fw-bold">0.00 DZD</div>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-2">
                                                            <div class="col-6">
                                                                <small class="text-muted">Coefficient:</small>
                                                                <div id="calculatedCoefficient" class="fw-bold">0.00</div>
                                                            </div>
                                                            <div class="col-6">
                                                                <small class="text-muted">Total Payé TTC:</small>
                                                                <div id="calculatedTotalTtc" class="fw-bold">0.00 DZD</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Informations logistiques -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>Informations Logistiques et Bancaires</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="invoiceNumber" class="form-label">Numéro de Facture</label>
                                                    <input type="text" class="form-control" id="invoiceNumber">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="invoiceDate" class="form-label">Date de Facture</label>
                                                    <input type="date" class="form-control" id="invoiceDate">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="bankName" class="form-label">Banque</label>
                                                    <select class="form-select" id="bankName">
                                                        <option value="">Sélectionner une banque</option>
                                                        <option value="Banque Nationale d'Algérie">Banque Nationale d'Algérie</option>
                                                        <option value="Crédit Populaire d'Algérie">Crédit Populaire d'Algérie</option>
                                                        <option value="BADR Bank">BADR Bank</option>
                                                        <option value="Banque Extérieure d'Algérie">Banque Extérieure d'Algérie</option>
                                                        <option value="Société Générale Algérie">Société Générale Algérie</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="lcNumber" class="form-label">Numéro LC</label>
                                                    <input type="text" class="form-control" id="lcNumber">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="lcValidationDate" class="form-label">Date Validation LC</label>
                                                    <input type="date" class="form-control" id="lcValidationDate">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="operationType" class="form-label">Type d'Opération</label>
                                                    <select class="form-select" id="operationType">
                                                        <option value="Import Commercial">Import Commercial</option>
                                                        <option value="Import Industriel">Import Industriel</option>
                                                        <option value="Import Urgent">Import Urgent</option>
                                                        <option value="Import Échantillons">Import Échantillons</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="paymentTerm" class="form-label">Conditions de Paiement</label>
                                                    <select class="form-select" id="paymentTerm">
                                                        <option value="">Sélectionner</option>
                                                        <option value="15 jours après B/L">15 jours après B/L</option>
                                                        <option value="30 jours après B/L">30 jours après B/L</option>
                                                        <option value="45 jours après B/L">45 jours après B/L</option>
                                                        <option value="60 jours après B/L">60 jours après B/L</option>
                                                        <option value="À vue">À vue</option>
                                                        <option value="Paiement anticipé">Paiement anticipé</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="priceTerm" class="form-label">Conditions de Prix</label>
                                                    <select class="form-select" id="priceTerm">
                                                        <option value="">Sélectionner</option>
                                                        <option value="FOB">FOB (Free On Board)</option>
                                                        <option value="CIF">CIF (Cost, Insurance, Freight)</option>
                                                        <option value="CFR">CFR (Cost and Freight)</option>
                                                        <option value="EXW">EXW (Ex Works)</option>
                                                        <option value="DDP">DDP (Delivered Duty Paid)</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="quantityPcs" class="form-label">Quantité (Pièces)</label>
                                                    <input type="number" class="form-control" id="quantityPcs">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="numContainers20ft" class="form-label">Conteneurs 20ft</label>
                                                    <input type="number" class="form-control" id="numContainers20ft" min="0">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="numContainers40ft" class="form-label">Conteneurs 40ft</label>
                                                    <input type="number" class="form-control" id="numContainers40ft" min="0">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="numPackages" class="form-label">Nombre de Colis</label>
                                                    <input type="number" class="form-control" id="numPackages" min="0">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Boutons d'action -->
                                <div class="d-flex justify-content-end gap-2">
                                    <button type="button" class="btn btn-outline-secondary" data-page="orders">Annuler</button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Enregistrer
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Page Détail Commande -->
                        <div id="orderDetailPage" class="page-content d-none">
                            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                                <h1 class="h2" id="orderDetailTitle">Détail de la Commande</h1>
                                <div class="btn-toolbar mb-2 mb-md-0">
                                    <button type="button" class="btn btn-outline-secondary me-2" data-page="orders">
                                        <i class="bi bi-arrow-left me-2"></i>Retour à la liste
                                    </button>
                                    <button type="button" class="btn btn-primary me-2" id="editOrderBtn">
                                        <i class="bi bi-pencil me-2"></i>Modifier
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" id="deleteOrderBtn">
                                        <i class="bi bi-trash me-2"></i>Supprimer
                                    </button>
                                </div>
                            </div>

                            <!-- Informations générales de la commande -->
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="card mb-4">
                                        <div class="card-header">
                                            <h5><i class="bi bi-info-circle me-2"></i>Informations Générales</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Numéro de Commande</label>
                                                        <div class="fw-bold" id="detailOrderNumber">-</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Date de Commande</label>
                                                        <div id="detailOrderDate">-</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Fournisseur</label>
                                                        <div id="detailSupplierName">-</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Type d'Expédition</label>
                                                        <div id="detailShipmentType">-</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Origine</label>
                                                        <div id="detailFromLocation">-</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Destination</label>
                                                        <div id="detailToLocation">-</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Description des Marchandises</label>
                                                        <div id="detailGoodsDescription">-</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card mb-4">
                                        <div class="card-header">
                                            <h5><i class="bi bi-calculator me-2"></i>Résumé Financier</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label text-muted">FOB</label>
                                                <div class="d-flex justify-content-between">
                                                    <span id="detailFobAmount">-</span>
                                                    <span id="detailFobCurrency">-</span>
                                                </div>
                                                <small class="text-muted">DZD: <span id="detailFobDzd">-</span></small>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Fret</label>
                                                <div id="detailFreightDzd">-</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">CIF Total</label>
                                                <div class="fw-bold text-primary" id="detailCifDzd">-</div>
                                            </div>
                                            <hr>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Coût d'Atterrissage HT</label>
                                                <div class="fw-bold text-success" id="detailLandedCost">-</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Coefficient</label>
                                                <div class="fw-bold" id="detailCoefficient">-</div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Total Payé TTC</label>
                                                <div class="fw-bold text-danger" id="detailTotalTtc">-</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Onglets pour les détails -->
                            <ul class="nav nav-tabs" id="orderDetailTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="items-detail-tab" data-bs-toggle="tab" data-bs-target="#items-detail" type="button" role="tab">
                                        <i class="bi bi-list-ul me-2"></i>Articles (<span id="itemsCount">0</span>)
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="costs-detail-tab" data-bs-toggle="tab" data-bs-target="#costs-detail" type="button" role="tab">
                                        <i class="bi bi-cash-stack me-2"></i>Allocations de Coûts (<span id="costsCount">0</span>)
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="arrival-detail-tab" data-bs-toggle="tab" data-bs-target="#arrival-detail" type="button" role="tab">
                                        <i class="bi bi-ship me-2"></i>Avis d'Arrivée (<span id="arrivalCount">0</span>)
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="financial-detail-tab" data-bs-toggle="tab" data-bs-target="#financial-detail" type="button" role="tab">
                                        <i class="bi bi-bank me-2"></i>Informations Financières
                                    </button>
                                </li>
                            </ul>

                            <div class="tab-content" id="orderDetailTabContent">
                                <!-- Onglet Articles -->
                                <div class="tab-pane fade show active" id="items-detail" role="tabpanel">
                                    <div class="card mt-3">
                                        <div class="card-header d-flex justify-content-between">
                                            <h6>Articles de la Commande</h6>
                                            <button type="button" class="btn btn-sm btn-outline-success" id="exportItemsBtn">
                                                <i class="bi bi-download me-1"></i>Exporter
                                            </button>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>N°</th>
                                                            <th>Référence</th>
                                                            <th>Description</th>
                                                            <th>Quantité</th>
                                                            <th>Prix Unit. FOB</th>
                                                            <th>Montant FOB</th>
                                                            <th>Prix Unit. FOB DZD</th>
                                                            <th>Prix Unit. Revient</th>
                                                            <th>Coût Total Revient</th>
                                                            <th>Surcoût Unit.</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="detailItemsTable">
                                                        <!-- Données chargées dynamiquement -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Onglet Allocations de Coûts -->
                                <div class="tab-pane fade" id="costs-detail" role="tabpanel">
                                    <div class="card mt-3">
                                        <div class="card-header">
                                            <h6>Allocations de Coûts</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Type</th>
                                                            <th>Nom du Coût</th>
                                                            <th>N° Facture</th>
                                                            <th>Date Facture</th>
                                                            <th>Montant HT</th>
                                                            <th>TVA</th>
                                                            <th>Total TTC</th>
                                                            <th>Type Saisie</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="detailCostsTable">
                                                        <!-- Données chargées dynamiquement -->
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-md-6">
                                                    <div class="card bg-light">
                                                        <div class="card-body">
                                                            <h6>Totaux HT</h6>
                                                            <div class="row">
                                                                <div class="col-6">
                                                                    <small>Douanes:</small>
                                                                    <div class="fw-bold" id="totalCustomsHT">0 DZD</div>
                                                                </div>
                                                                <div class="col-6">
                                                                    <small>Livraison:</small>
                                                                    <div class="fw-bold" id="totalDeliveryHT">0 DZD</div>
                                                                </div>
                                                            </div>
                                                            <div class="row mt-2">
                                                                <div class="col-6">
                                                                    <small>Agence:</small>
                                                                    <div class="fw-bold" id="totalAgencyHT">0 DZD</div>
                                                                </div>
                                                                <div class="col-6">
                                                                    <small>Autres:</small>
                                                                    <div class="fw-bold" id="totalOtherHT">0 DZD</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card bg-primary text-white">
                                                        <div class="card-body">
                                                            <h6>Total Général</h6>
                                                            <div class="row">
                                                                <div class="col-6">
                                                                    <small>Total HT:</small>
                                                                    <div class="fw-bold" id="grandTotalHT">0 DZD</div>
                                                                </div>
                                                                <div class="col-6">
                                                                    <small>Total TTC:</small>
                                                                    <div class="fw-bold" id="grandTotalTTC">0 DZD</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Onglet Avis d'Arrivée -->
                                <div class="tab-pane fade" id="arrival-detail" role="tabpanel">
                                    <div class="card mt-3">
                                        <div class="card-header">
                                            <h6>Avis d'Arrivée</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="detailArrivalNotices">
                                                <!-- Données chargées dynamiquement -->
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Onglet Informations Financières -->
                                <div class="tab-pane fade" id="financial-detail" role="tabpanel">
                                    <div class="card mt-3">
                                        <div class="card-header">
                                            <h6>Détails Financiers et Bancaires</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6>Informations Bancaires</h6>
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Banque</label>
                                                        <div id="detailBankName">-</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Numéro LC</label>
                                                        <div id="detailLcNumber">-</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Date Validation LC</label>
                                                        <div id="detailLcValidationDate">-</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>Conditions Commerciales</h6>
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Conditions de Paiement</label>
                                                        <div id="detailPaymentTerm">-</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Conditions de Prix</label>
                                                        <div id="detailPriceTerm">-</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label text-muted">Type d'Opération</label>
                                                        <div id="detailOperationType">-</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <hr>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <h6>Taux de Change</h6>
                                                    <div class="mb-2">
                                                        <small>USD/DZD:</small>
                                                        <div class="fw-bold" id="detailRateUsd">-</div>
                                                    </div>
                                                    <div class="mb-2">
                                                        <small>EUR/DZD:</small>
                                                        <div class="fw-bold" id="detailRateEur">-</div>
                                                    </div>
                                                    <div class="mb-2">
                                                        <small>Yuan/DZD:</small>
                                                        <div class="fw-bold" id="detailRateYuan">-</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <h6>Quantités</h6>
                                                    <div class="mb-2">
                                                        <small>Pièces:</small>
                                                        <div class="fw-bold" id="detailQuantityPcs">-</div>
                                                    </div>
                                                    <div class="mb-2">
                                                        <small>Conteneurs 20ft:</small>
                                                        <div class="fw-bold" id="detailContainers20">-</div>
                                                    </div>
                                                    <div class="mb-2">
                                                        <small>Conteneurs 40ft:</small>
                                                        <div class="fw-bold" id="detailContainers40">-</div>
                                                    </div>
                                                    <div class="mb-2">
                                                        <small>Packages:</small>
                                                        <div class="fw-bold" id="detailPackages">-</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <h6>Facture</h6>
                                                    <div class="mb-2">
                                                        <small>N° Facture:</small>
                                                        <div class="fw-bold" id="detailInvoiceNumber">-</div>
                                                    </div>
                                                    <div class="mb-2">
                                                        <small>Date Facture:</small>
                                                        <div class="fw-bold" id="detailInvoiceDate">-</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Page Rapports -->
                        <div id="reportsPage" class="page-content d-none">
                            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                                <h1 class="h2">Rapports et Analyses</h1>
                            </div>

                            <!-- Filtres pour les rapports -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>Filtres</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label for="reportDateFrom" class="form-label">Date début</label>
                                            <input type="date" class="form-control" id="reportDateFrom">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="reportDateTo" class="form-label">Date fin</label>
                                            <input type="date" class="form-control" id="reportDateTo">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="reportShipmentType" class="form-label">Type d'expédition</label>
                                            <select class="form-select" id="reportShipmentType">
                                                <option value="">Tous</option>
                                                <option value="SEA">Maritime</option>
                                                <option value="AIR">Aérien</option>
                                                <option value="Express">Express</option>
                                                <option value="Other">Autre</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="reportSupplier" class="form-label">Fournisseur</label>
                                            <input type="text" class="form-control" id="reportSupplier" placeholder="Nom du fournisseur">
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <button type="button" class="btn btn-primary" id="generateReportsBtn">
                                                <i class="bi bi-bar-chart me-2"></i>Générer Rapports
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" id="clearReportFiltersBtn">
                                                <i class="bi bi-x-circle me-2"></i>Effacer Filtres
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Onglets des rapports -->
                            <ul class="nav nav-tabs" id="reportsTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="tracking-tab" data-bs-toggle="tab" data-bs-target="#tracking" type="button" role="tab">
                                        Suivi des Commandes
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="costs-tab" data-bs-toggle="tab" data-bs-target="#costs" type="button" role="tab">
                                        Analyse des Coûts
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="margins-tab" data-bs-toggle="tab" data-bs-target="#margins" type="button" role="tab">
                                        Analyse des Marges
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="charts-tab" data-bs-toggle="tab" data-bs-target="#charts" type="button" role="tab">
                                        Graphiques
                                    </button>
                                </li>
                            </ul>

                            <div class="tab-content" id="reportsTabContent">
                                <!-- Rapport de suivi -->
                                <div class="tab-pane fade show active" id="tracking" role="tabpanel">
                                    <div class="card mt-3">
                                        <div class="card-header d-flex justify-content-between">
                                            <h5>Suivi des Commandes</h5>
                                            <button type="button" class="btn btn-sm btn-outline-success" id="exportTrackingBtn">
                                                <i class="bi bi-download me-1"></i>Exporter
                                            </button>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped" id="trackingReportTable">
                                                    <thead>
                                                        <tr>
                                                            <th>N° Commande</th>
                                                            <th>Date</th>
                                                            <th>Fournisseur</th>
                                                            <th>Type</th>
                                                            <th>Statut</th>
                                                            <th>CIF DZD</th>
                                                            <th>Coût d'Atterrissage</th>
                                                            <th>Coefficient</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="trackingReportBody">
                                                        <!-- Données générées dynamiquement -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Rapport d'analyse des coûts -->
                                <div class="tab-pane fade" id="costs" role="tabpanel">
                                    <div class="card mt-3">
                                        <div class="card-header">
                                            <h5>Analyse Détaillée des Coûts</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped" id="costsReportTable">
                                                    <thead>
                                                        <tr>
                                                            <th>N° Commande</th>
                                                            <th>FOB DZD</th>
                                                            <th>Fret DZD</th>
                                                            <th>CIF DZD</th>
                                                            <th>Douanes HT</th>
                                                            <th>Livraison HT</th>
                                                            <th>Agence HT</th>
                                                            <th>Autres HT</th>
                                                            <th>Total Allocations</th>
                                                            <th>Coût d'Atterrissage</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="costsReportBody">
                                                        <!-- Données générées dynamiquement -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Rapport d'analyse des marges -->
                                <div class="tab-pane fade" id="margins" role="tabpanel">
                                    <div class="card mt-3">
                                        <div class="card-header">
                                            <h5>Analyse des Marges par Article</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped" id="marginsReportTable">
                                                    <thead>
                                                        <tr>
                                                            <th>N° Commande</th>
                                                            <th>Référence</th>
                                                            <th>Description</th>
                                                            <th>Quantité</th>
                                                            <th>Prix FOB Unit.</th>
                                                            <th>Coût Revient Unit.</th>
                                                            <th>Surcoût Unit.</th>
                                                            <th>% Augmentation</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="marginsReportBody">
                                                        <!-- Données générées dynamiquement -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Graphiques -->
                                <div class="tab-pane fade" id="charts" role="tabpanel">
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6>Coûts par Type d'Expédition</h6>
                                                </div>
                                                <div class="card-body">
                                                    <canvas id="shipmentTypeChart" width="400" height="200"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6>Top 5 Fournisseurs</h6>
                                                </div>
                                                <div class="card-body">
                                                    <canvas id="topSuppliersChart" width="400" height="200"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-12">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6>Évolution Mensuelle des Coûts</h6>
                                                </div>
                                                <div class="card-body">
                                                    <canvas id="monthlyEvolutionChart" width="800" height="300"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Page Import/Export -->
                        <div id="importExportPage" class="page-content d-none">
                            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                                <h1 class="h2">Import/Export Excel</h1>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="bi bi-download me-2"></i>Import Excel</h5>
                                        </div>
                                        <div class="card-body">
                                            <p>Importez des commandes depuis un fichier Excel.</p>
                                            <button type="button" class="btn btn-primary" id="importExcelBtn">
                                                <i class="bi bi-file-earmark-excel me-2"></i>Sélectionner fichier Excel
                                            </button>
                                            <div id="importProgress" class="mt-3 d-none">
                                                <div class="progress">
                                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                                </div>
                                            </div>
                                            <div id="importResult" class="mt-3"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="bi bi-upload me-2"></i>Export Excel</h5>
                                        </div>
                                        <div class="card-body">
                                            <p>Exportez toutes les commandes vers un fichier Excel.</p>
                                            <button type="button" class="btn btn-success" id="exportExcelBtn">
                                                <i class="bi bi-file-earmark-excel me-2"></i>Exporter vers Excel
                                            </button>
                                            <div id="exportResult" class="mt-3"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </main>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading overlay -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/app.js"></script>
</body>
</html>

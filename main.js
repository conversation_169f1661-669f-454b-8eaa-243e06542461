const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const Database = require('./src/database/database');
const AuthService = require('./src/services/authService');
const OrderService = require('./src/services/orderService');
const ExcelService = require('./src/services/excelService');
const ReportService = require('./src/services/reportService');

class MainApp {
  constructor() {
    this.mainWindow = null;
    this.database = null;
    this.authService = null;
    this.orderService = null;
    this.excelService = null;
    this.reportService = null;
  }

  async initialize() {
    // Initialiser la base de données
    this.database = new Database();
    await this.database.initialize();

    // Initialiser les services
    this.authService = new AuthService(this.database);
    this.orderService = new OrderService(this.database);
    this.excelService = new ExcelService();
    this.reportService = new ReportService(this.database);

    // Configurer les gestionnaires IPC
    this.setupIpcHandlers();
  }

  createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      },
      icon: path.join(__dirname, 'assets/icon.png'),
      show: false
    });

    this.mainWindow.loadFile('src/renderer/index.html');

    // Afficher la fenêtre quand elle est prête
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
    });

    // Ouvrir DevTools en mode développement
    if (process.argv.includes('--dev')) {
      this.mainWindow.webContents.openDevTools();
    }
  }

  setupIpcHandlers() {
    // Authentification
    ipcMain.handle('auth:login', async (event, credentials) => {
      try {
        const result = await this.authService.login(credentials.username, credentials.password);
        return { success: true, user: result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('auth:register', async (event, userData) => {
      try {
        const result = await this.authService.register(userData);
        return { success: true, user: result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Gestion des commandes
    ipcMain.handle('orders:getAll', async () => {
      try {
        const orders = await this.orderService.getAllOrders();
        return { success: true, data: orders };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('orders:getById', async (event, orderId) => {
      try {
        const order = await this.orderService.getOrderById(orderId);
        return { success: true, data: order };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('orders:create', async (event, orderData) => {
      try {
        const order = await this.orderService.createOrder(orderData);
        return { success: true, data: order };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('orders:update', async (event, orderId, orderData) => {
      try {
        const order = await this.orderService.updateOrder(orderId, orderData);
        return { success: true, data: order };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('orders:delete', async (event, orderId) => {
      try {
        await this.orderService.deleteOrder(orderId);
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Import/Export Excel
    ipcMain.handle('excel:import', async () => {
      try {
        const result = await dialog.showOpenDialog(this.mainWindow, {
          properties: ['openFile'],
          filters: [
            { name: 'Excel Files', extensions: ['xlsx', 'xls'] }
          ]
        });

        if (!result.canceled && result.filePaths.length > 0) {
          const data = await this.excelService.importFromExcel(result.filePaths[0]);
          return { success: true, data };
        }
        return { success: false, error: 'Aucun fichier sélectionné' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('excel:export', async (event, data) => {
      try {
        const result = await dialog.showSaveDialog(this.mainWindow, {
          filters: [
            { name: 'Excel Files', extensions: ['xlsx'] }
          ],
          defaultPath: 'export.xlsx'
        });

        if (!result.canceled) {
          await this.excelService.exportToExcel(data, result.filePath);
          return { success: true, filePath: result.filePath };
        }
        return { success: false, error: 'Export annulé' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Rapports
    ipcMain.handle('reports:getOrderTracking', async (event, filters) => {
      try {
        const data = await this.reportService.getOrderTrackingReport(filters);
        return { success: true, data };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('reports:getCostAnalysis', async (event, orderId) => {
      try {
        const data = await this.reportService.getCostAnalysisReport(orderId);
        return { success: true, data };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('reports:getMarginAnalysis', async () => {
      try {
        const data = await this.reportService.getMarginAnalysisReport();
        return { success: true, data };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('reports:getChartData', async (event, chartType) => {
      try {
        const data = await this.reportService.getChartData(chartType);
        return { success: true, data };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('reports:getTopSuppliers', async (event, limit) => {
      try {
        const data = await this.reportService.getTopSuppliers(limit);
        return { success: true, data };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('reports:getCostEvolution', async (event, period) => {
      try {
        const data = await this.reportService.getCostEvolutionByPeriod(period);
        return { success: true, data };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });
  }
}

const mainApp = new MainApp();

app.whenReady().then(async () => {
  await mainApp.initialize();
  mainApp.createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      mainApp.createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

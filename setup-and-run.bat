@echo off
title Setup et Lancement - Application de Gestion des Importations
color 0A

echo.
echo ========================================
echo   APPLICATION DE GESTION DES IMPORTATIONS
echo ========================================
echo   Setup et Lancement Automatique
echo ========================================
echo.

REM Aller dans le répertoire du script
cd /d "%~dp0"

echo 🔍 Vérification de l'environnement...
echo.

REM Vérifier Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js n'est pas installé
    echo.
    echo 📥 Téléchargement et installation automatique...
    echo Redirection vers https://nodejs.org/
    start https://nodejs.org/
    echo.
    echo ⚠️  Veuillez installer Node.js et relancer ce script
    pause
    exit /b 1
) else (
    echo ✅ Node.js installé
)

REM Vérifier npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm n'est pas disponible
    pause
    exit /b 1
) else (
    echo ✅ npm disponible
)

echo.
echo 📦 Vérification des dépendances...

REM Installer les dépendances si nécessaire
if not exist "node_modules" (
    echo.
    echo 📥 Installation des dépendances npm...
    echo Cela peut prendre quelques minutes...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Erreur lors de l'installation
        pause
        exit /b 1
    )
    echo.
    echo ✅ Dépendances installées avec succès
) else (
    echo ✅ Dépendances déjà installées
)

REM Créer le répertoire data
if not exist "data" (
    echo 📁 Création du répertoire data...
    mkdir data
)

echo.
echo 🔧 Préparation de la base de données...
echo.

REM Exécuter les migrations si nécessaire
if exist "migrate-to-complete-model.js" (
    echo 🔄 Application des migrations...
    node migrate-to-complete-model.js
    echo.
)

echo ========================================
echo   LANCEMENT DE L'APPLICATION
echo ========================================
echo.
echo 👤 Utilisateur : admin
echo 🔑 Mot de passe : admin123
echo.
echo ⚠️  Les erreurs GPU sont normales
echo.
echo 🚀 Démarrage...
echo.

REM Lancer l'application
npm run dev

echo.
echo ========================================
echo   APPLICATION FERMÉE
echo ========================================
echo.
pause

// Script de test pour toutes les fonctionnalités CRUD améliorées
const Database = require('./src/database/database');
const OrderService = require('./src/services/orderService');

async function testCRUDFeatures() {
    console.log('🧪 Test des fonctionnalités CRUD améliorées...');
    
    const database = new Database();
    await database.initialize();
    
    const orderService = new OrderService(database);

    try {
        // Test 1: Création d'une commande complète avec tous les champs
        console.log('\n📝 Test 1: Création d\'une commande complète...');
        const newOrderData = {
            order_number: 'TEST-CRUD-' + Date.now(),
            order_date: '2024-03-01',
            supplier_name: 'Fournisseur Test CRUD',
            shipment_type: 'SEA',
            invoice_number: 'INV-TEST-001',
            invoice_date: '2024-02-28',
            from_location: 'Port de Test',
            to_location: 'Port d\'Alger',
            operation_type: 'Import Commercial',
            goods_description: 'Marchandises de test pour CRUD',
            bank_name: 'Banque Test',
            lc_number: 'LC-TEST-001',
            lc_validation_date: '2024-02-25',
            payment_term: '30 jours après B/L',
            price_term: 'FOB',
            quantity_pcs: 1000,
            num_containers_20ft: 1,
            num_containers_40ft: 0,
            num_packages: 50,
            exchange_rate_usd_dzd: 135.00,
            exchange_rate_eur_dzd: 146.00,
            exchange_rate_yuan_dzd: 20.00,
            fob_amount_currency: 50000.00,
            fob_currency: 'USD',
            freight_amount_currency: 3000.00,
            
            items: [
                {
                    item_number: 1,
                    part_number: 'TEST-001',
                    description: 'Article de test 1',
                    quantity: 500,
                    unit_fob: 80.00,
                    amount: 40000.00
                },
                {
                    item_number: 2,
                    part_number: 'TEST-002',
                    description: 'Article de test 2',
                    quantity: 500,
                    unit_fob: 20.00,
                    amount: 10000.00
                }
            ],
            
            arrivalNotices: [
                {
                    voyage_number: 'TEST-VOYAGE-001',
                    bill_of_lading_number: 'BL-TEST-001',
                    vessel_name: 'Navire Test',
                    shipowner: 'Armateur Test',
                    actual_time_of_arrival: '2024-03-15'
                }
            ],
            
            costAllocations: [
                {
                    cost_type: 'CustomsDuties1',
                    cost_name: 'Droits de douane test',
                    invoice_number: 'DOUANE-TEST-001',
                    invoice_date: '2024-03-16',
                    total_ttc: 500000.00,
                    tva: 84746.00,
                    ht: 415254.00,
                    is_manual_entry: false
                },
                {
                    cost_type: 'ImportDelivery',
                    cost_name: 'Livraison test',
                    invoice_number: 'LIV-TEST-001',
                    invoice_date: '2024-03-17',
                    total_ttc: 120000.00,
                    tva: 20339.00,
                    ht: 99661.00,
                    is_manual_entry: false
                }
            ]
        };

        const createResult = await orderService.createOrder(newOrderData);
        if (createResult && createResult.id) {
            console.log('✅ Commande créée avec succès:', createResult.order_number);
            console.log(`   💰 CIF DZD: ${createResult.total_cif_dzd?.toLocaleString('fr-FR')} DZD`);
            console.log(`   🏭 Coût d'atterrissage: ${createResult.landed_cost_ht?.toLocaleString('fr-FR')} DZD`);
            console.log(`   📊 Coefficient: ${createResult.landed_cost_coefficient?.toFixed(4)}`);

            const orderId = createResult.id;

            // Test 2: Lecture complète avec tous les détails
            console.log('\n📖 Test 2: Lecture complète de la commande...');
            const readResult = await orderService.getOrderById(orderId);
            if (readResult && readResult.id) {
                console.log('✅ Commande lue avec succès');
                console.log(`   📋 Articles: ${readResult.items?.length || 0}`);
                console.log(`   🚢 Avis d'arrivée: ${readResult.arrivalNotices?.length || 0}`);
                console.log(`   💸 Allocations: ${readResult.costAllocations?.length || 0}`);

                // Vérifier les calculs automatiques
                const order = readResult;
                console.log('\n🧮 Vérification des calculs automatiques:');
                console.log(`   FOB DZD: ${order.fob_amount_dzd?.toLocaleString('fr-FR')} DZD`);
                console.log(`   Fret DZD: ${order.freight_amount_dzd?.toLocaleString('fr-FR')} DZD`);
                console.log(`   CIF DZD: ${order.total_cif_dzd?.toLocaleString('fr-FR')} DZD`);
                console.log(`   Total allocations HT: ${order.total_cost_allocations_ht?.toLocaleString('fr-FR')} DZD`);
                console.log(`   Landed Cost HT: ${order.landed_cost_ht?.toLocaleString('fr-FR')} DZD`);
                console.log(`   Coefficient: ${order.landed_cost_coefficient?.toFixed(4)}`);
                
                // Vérifier les coûts de revient des articles
                if (order.items && order.items.length > 0) {
                    console.log('\n📦 Coûts de revient des articles:');
                    order.items.forEach(item => {
                        console.log(`   ${item.part_number}: ${item.unit_fob} → ${item.unit_cost_price?.toFixed(2)} DZD (+${(item.unit_cost_price - item.unit_fob_dzd)?.toFixed(2)} DZD)`);
                    });
                }
            } else {
                console.log('❌ Erreur lors de la lecture:', readResult ? 'Pas de données' : 'Résultat undefined');
            }

            // Test 3: Mise à jour avec modification des données
            console.log('\n✏️ Test 3: Mise à jour de la commande...');
            const updateData = {
                ...newOrderData,
                supplier_name: 'Fournisseur Test CRUD - Modifié',
                goods_description: 'Marchandises de test pour CRUD - Modifiées',
                fob_amount_currency: 55000.00, // Augmentation du FOB
                
                // Ajouter un nouvel article
                items: [
                    ...newOrderData.items,
                    {
                        item_number: 3,
                        part_number: 'TEST-003',
                        description: 'Nouvel article ajouté',
                        quantity: 200,
                        unit_fob: 15.00,
                        amount: 3000.00
                    }
                ],
                
                // Ajouter une nouvelle allocation
                costAllocations: [
                    ...newOrderData.costAllocations,
                    {
                        cost_type: 'OtherMiscellaneous',
                        cost_name: 'Frais divers ajoutés',
                        invoice_number: 'DIVERS-TEST-001',
                        invoice_date: '2024-03-18',
                        total_ttc: 50000.00,
                        tva: 8475.00,
                        ht: 41525.00,
                        is_manual_entry: true
                    }
                ]
            };

            const updateResult = await orderService.updateOrder(orderId, updateData);
            if (updateResult && updateResult.id) {
                console.log('✅ Commande mise à jour avec succès');
                console.log(`   💰 Nouveau CIF DZD: ${updateResult.total_cif_dzd?.toLocaleString('fr-FR')} DZD`);
                console.log(`   🏭 Nouveau coût d'atterrissage: ${updateResult.landed_cost_ht?.toLocaleString('fr-FR')} DZD`);
                console.log(`   📊 Nouveau coefficient: ${updateResult.landed_cost_coefficient?.toFixed(4)}`);
                console.log(`   📋 Nombre d'articles: ${updateResult.items?.length || 0}`);
                console.log(`   💸 Nombre d'allocations: ${updateResult.costAllocations?.length || 0}`);
            } else {
                console.log('❌ Erreur lors de la mise à jour:', updateResult ? 'Pas de données' : 'Résultat undefined');
            }

            // Test 4: Test des requêtes de liste avec filtres
            console.log('\n📋 Test 4: Requêtes de liste avec filtres...');
            const listResult = await orderService.getAllOrders();
            if (listResult && Array.isArray(listResult)) {
                console.log(`✅ Liste récupérée: ${listResult.length} commandes`);

                // Afficher les statistiques
                const orders = listResult;
                const totalValue = orders.reduce((sum, order) => sum + (order.total_cif_dzd || 0), 0);
                const avgValue = orders.length > 0 ? totalValue / orders.length : 0;
                const avgCoeff = orders.length > 0 ? 
                    orders.reduce((sum, order) => sum + (order.landed_cost_coefficient || 0), 0) / orders.length : 0;
                
                console.log(`   💰 Valeur totale: ${totalValue.toLocaleString('fr-FR')} DZD`);
                console.log(`   📊 Valeur moyenne: ${avgValue.toLocaleString('fr-FR')} DZD`);
                console.log(`   🧮 Coefficient moyen: ${avgCoeff.toFixed(4)}`);
            } else {
                console.log('❌ Erreur lors de la récupération de la liste:', listResult ? 'Pas un tableau' : 'Résultat undefined');
            }

            // Test 5: Suppression (optionnel - décommenter pour tester)
            /*
            console.log('\n🗑️ Test 5: Suppression de la commande...');
            const deleteResult = await orderService.deleteOrder(orderId);
            if (deleteResult.success) {
                console.log('✅ Commande supprimée avec succès');
            } else {
                console.log('❌ Erreur lors de la suppression:', deleteResult.error);
            }
            */

        } else {
            console.log('❌ Erreur lors de la création:', createResult ? 'Pas de données' : 'Résultat undefined');
        }

    } catch (error) {
        console.error('❌ Erreur générale:', error);
    }

    await database.close();
    console.log('\n🎉 Tests CRUD terminés !');
    console.log('\n📊 Résumé des fonctionnalités testées:');
    console.log('   ✅ Création complète avec tous les champs');
    console.log('   ✅ Lecture avec jointures et calculs');
    console.log('   ✅ Mise à jour avec recalculs automatiques');
    console.log('   ✅ Requêtes de liste avec statistiques');
    console.log('   ✅ Calculs automatiques (conversions, coefficients, coûts de revient)');
    console.log('   ✅ Gestion des articles avec coûts de revient');
    console.log('   ✅ Gestion des avis d\'arrivée');
    console.log('   ✅ Gestion des allocations de coûts');
    console.log('\n🚀 Toutes les fonctionnalités CRUD avancées sont opérationnelles !');
}

// Exécuter les tests
testCRUDFeatures().catch(console.error);

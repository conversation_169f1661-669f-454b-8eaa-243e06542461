// Application principale
class ImportManagementApp {
    constructor() {
        this.currentUser = null;
        this.currentPage = 'dashboard';
        this.orders = [];
        this.editingOrderId = null;
        this.orderItems = [];
        this.arrivalNotices = [];
        this.costAllocations = [];
        this.charts = {};
        this.currentOrderDetail = null;
        this.sortField = 'order_date';
        this.sortDirection = 'desc';
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filteredOrders = [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupAdvancedSearch();
        this.setupKeyboardShortcuts();
        this.showLoginPage();

        // Charger un brouillon si disponible
        setTimeout(() => {
            this.loadOrderDraft();
        }, 1000);
    }

    setupEventListeners() {
        // Formulaire de connexion
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Déconnexion
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.handleLogout();
        });

        // Navigation
        document.querySelectorAll('[data-page]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = e.target.closest('[data-page]').dataset.page;
                this.navigateToPage(page);
            });
        });

        // Formulaire de commande
        document.getElementById('orderForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleOrderSubmit();
        });

        // Import/Export Excel
        document.getElementById('importExcelBtn').addEventListener('click', () => {
            this.handleImportExcel();
        });

        document.getElementById('exportExcelBtn').addEventListener('click', () => {
            this.handleExportExcel();
        });

        // Filtres des commandes
        document.getElementById('searchOrders').addEventListener('input', () => {
            this.filterOrders();
        });

        document.getElementById('filterShipmentType').addEventListener('change', () => {
            this.filterOrders();
        });

        document.getElementById('filterDateFrom').addEventListener('change', () => {
            this.filterOrders();
        });

        document.getElementById('filterDateTo').addEventListener('change', () => {
            this.filterOrders();
        });

        document.getElementById('clearFilters').addEventListener('click', () => {
            this.clearFilters();
        });

        // Gestion des articles
        document.getElementById('addItemBtn').addEventListener('click', () => {
            this.addOrderItem();
        });

        // Gestion des avis d'arrivée
        document.getElementById('addArrivalNoticeBtn').addEventListener('click', () => {
            this.addArrivalNotice();
        });

        // Gestion des allocations de coûts
        document.getElementById('addCostAllocationBtn').addEventListener('click', () => {
            this.addCostAllocation();
        });

        // Rapports
        document.getElementById('generateReportsBtn').addEventListener('click', () => {
            this.generateReports();
        });

        document.getElementById('clearReportFiltersBtn').addEventListener('click', () => {
            this.clearReportFilters();
        });

        // Calculs en temps réel
        ['fobAmountCurrency', 'freightAmountCurrency', 'exchangeRateUsdDzd', 'exchangeRateEurDzd', 'exchangeRateYuanDzd'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', () => {
                    this.updateCalculations();
                });
            }
        });

        // Gestion des commandes avancée
        document.getElementById('refreshOrdersBtn').addEventListener('click', () => {
            this.loadOrdersData();
        });

        document.getElementById('exportOrdersBtn').addEventListener('click', () => {
            this.exportFilteredOrders();
        });

        document.getElementById('selectAllOrders').addEventListener('change', (e) => {
            this.toggleSelectAllOrders(e.target.checked);
        });

        // Tri des colonnes
        document.querySelectorAll('[data-sort]').forEach(header => {
            header.addEventListener('click', (e) => {
                e.preventDefault();
                const field = e.target.closest('[data-sort]').dataset.sort;
                this.sortOrders(field);
            });
        });

        // Page de détail
        document.getElementById('editOrderBtn').addEventListener('click', () => {
            if (this.currentOrderDetail) {
                this.editOrder(this.currentOrderDetail.id);
            }
        });

        document.getElementById('deleteOrderBtn').addEventListener('click', () => {
            if (this.currentOrderDetail) {
                this.deleteOrder(this.currentOrderDetail.id);
            }
        });

        document.getElementById('exportItemsBtn').addEventListener('click', () => {
            this.exportOrderItems();
        });
    }

    showLoginPage() {
        document.getElementById('loginPage').classList.remove('d-none');
        document.getElementById('mainApp').classList.add('d-none');
    }

    showMainApp() {
        document.getElementById('loginPage').classList.add('d-none');
        document.getElementById('mainApp').classList.remove('d-none');
        this.navigateToPage('dashboard');
    }

    async handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const errorDiv = document.getElementById('loginError');

        try {
            this.showLoading(true);
            const result = await window.electronAPI.auth.login({ username, password });
            
            if (result.success) {
                this.currentUser = result.user;
                document.getElementById('currentUser').textContent = result.user.username;
                this.showMainApp();
                errorDiv.classList.add('d-none');
            } else {
                errorDiv.textContent = result.error;
                errorDiv.classList.remove('d-none');
            }
        } catch (error) {
            errorDiv.textContent = 'Erreur de connexion: ' + error.message;
            errorDiv.classList.remove('d-none');
        } finally {
            this.showLoading(false);
        }
    }

    handleLogout() {
        this.currentUser = null;
        this.orders = [];
        document.getElementById('username').value = '';
        document.getElementById('password').value = '';
        this.showLoginPage();
    }

    navigateToPage(page) {
        // Masquer toutes les pages
        document.querySelectorAll('.page-content').forEach(pageEl => {
            pageEl.classList.add('d-none');
        });

        // Mettre à jour la navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Afficher la page demandée
        const pageElement = document.getElementById(page + 'Page');
        if (pageElement) {
            pageElement.classList.remove('d-none');
            this.currentPage = page;

            // Marquer le lien de navigation comme actif
            const navLink = document.querySelector(`[data-page="${page}"]`);
            if (navLink) {
                navLink.classList.add('active');
            }

            // Charger les données spécifiques à la page
            this.loadPageData(page);
        }
    }

    async loadPageData(page) {
        switch (page) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'orders':
                await this.loadOrdersData();
                break;
            case 'new-order':
                this.resetOrderForm();
                break;
            case 'reports':
                await this.loadReportsData();
                break;
        }
    }

    async loadDashboardData() {
        try {
            this.showLoading(true);
            const result = await window.electronAPI.orders.getAll();
            
            if (result.success) {
                this.orders = result.data;
                this.updateDashboardStats();
                this.updateRecentOrdersTable();
            }
        } catch (error) {
            this.showError('Erreur lors du chargement des données: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    updateDashboardStats() {
        const totalOrders = this.orders.length;
        const totalValue = this.orders.reduce((sum, order) => sum + (order.total_cif_dzd || 0), 0);
        
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        const thisMonthOrders = this.orders.filter(order => {
            const orderDate = new Date(order.order_date);
            return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear;
        }).length;

        const avgValue = totalOrders > 0 ? totalValue / totalOrders : 0;

        document.getElementById('totalOrders').textContent = totalOrders;
        document.getElementById('totalValue').textContent = this.formatCurrency(totalValue) + ' DZD';
        document.getElementById('thisMonthOrders').textContent = thisMonthOrders;
        document.getElementById('avgValue').textContent = this.formatCurrency(avgValue) + ' DZD';
    }

    updateRecentOrdersTable() {
        const tbody = document.getElementById('recentOrdersTable');
        const recentOrders = this.orders.slice(0, 5); // 5 dernières commandes

        tbody.innerHTML = recentOrders.map(order => `
            <tr>
                <td>${order.order_number}</td>
                <td>${this.formatDate(order.order_date)}</td>
                <td>${order.supplier_name || '-'}</td>
                <td><span class="badge bg-${this.getShipmentTypeBadgeColor(order.shipment_type)}">${order.shipment_type}</span></td>
                <td>${this.formatCurrency(order.total_cif_dzd)} DZD</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="app.editOrder(${order.id})">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.deleteOrder(${order.id})">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    async loadOrdersData() {
        try {
            this.showLoading(true);
            const result = await window.electronAPI.orders.getAll();
            
            if (result.success) {
                this.orders = result.data;
                this.updateOrdersTable();
            }
        } catch (error) {
            this.showError('Erreur lors du chargement des commandes: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    updateOrdersTable() {
        // Appliquer les filtres et le tri
        this.applyFiltersAndSort();

        // Calculer la pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedOrders = this.filteredOrders.slice(startIndex, endIndex);

        const tbody = document.getElementById('ordersTable');

        tbody.innerHTML = paginatedOrders.map(order => {
            const status = this.getOrderStatus(order);
            return `
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input order-checkbox" value="${order.id}">
                    </td>
                    <td>
                        <a href="#" class="text-decoration-none fw-bold" onclick="app.viewOrder(${order.id})">
                            ${order.order_number}
                        </a>
                    </td>
                    <td>${this.formatDate(order.order_date)}</td>
                    <td>
                        <div class="fw-bold">${order.supplier_name || '-'}</div>
                        <small class="text-muted">${order.invoice_number || ''}</small>
                    </td>
                    <td>
                        <span class="badge bg-${this.getShipmentTypeBadgeColor(order.shipment_type)}">
                            ${order.shipment_type}
                        </span>
                    </td>
                    <td>
                        <div class="small">
                            <i class="bi bi-geo-alt text-muted"></i> ${order.from_location || '-'}
                            <br>
                            <i class="bi bi-geo-alt-fill text-primary"></i> ${order.to_location || '-'}
                        </div>
                    </td>
                    <td>
                        <div class="fw-bold text-primary">${this.formatCurrency(order.total_cif_dzd)} DZD</div>
                        <small class="text-muted">${order.fob_amount_currency} ${order.fob_currency}</small>
                    </td>
                    <td>
                        <div class="fw-bold text-success">${this.formatCurrency(order.landed_cost_ht)} DZD</div>
                        <small class="text-muted">+${this.formatCurrency(order.landed_cost_ht - order.total_cif_dzd)}</small>
                    </td>
                    <td>
                        <span class="badge bg-${this.getCoefficientBadgeColor(order.landed_cost_coefficient)}">
                            ${(order.landed_cost_coefficient || 0).toFixed(4)}
                        </span>
                    </td>
                    <td>
                        <span class="badge bg-info">${order.items_count || 0}</span>
                    </td>
                    <td>
                        <span class="badge bg-${status.color}">${status.text}</span>
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-info" onclick="app.viewOrder(${order.id})" title="Voir détails">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="app.editOrder(${order.id})" title="Modifier">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="app.deleteOrder(${order.id})" title="Supprimer">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        // Mettre à jour les statistiques
        this.updateOrdersStatistics();

        // Mettre à jour la pagination
        this.updatePagination();
    }

    applyFiltersAndSort() {
        const searchTerm = document.getElementById('searchOrders').value.toLowerCase();
        const shipmentType = document.getElementById('filterShipmentType').value;
        const dateFrom = document.getElementById('filterDateFrom').value;
        const dateTo = document.getElementById('filterDateTo').value;

        // Filtrer
        this.filteredOrders = this.orders.filter(order => {
            const matchesSearch = !searchTerm ||
                order.order_number.toLowerCase().includes(searchTerm) ||
                (order.supplier_name && order.supplier_name.toLowerCase().includes(searchTerm)) ||
                (order.from_location && order.from_location.toLowerCase().includes(searchTerm)) ||
                (order.to_location && order.to_location.toLowerCase().includes(searchTerm));

            const matchesShipmentType = !shipmentType || order.shipment_type === shipmentType;

            const orderDate = new Date(order.order_date);
            const matchesDateFrom = !dateFrom || orderDate >= new Date(dateFrom);
            const matchesDateTo = !dateTo || orderDate <= new Date(dateTo);

            return matchesSearch && matchesShipmentType && matchesDateFrom && matchesDateTo;
        });

        // Trier
        this.filteredOrders.sort((a, b) => {
            let aValue = a[this.sortField];
            let bValue = b[this.sortField];

            // Gestion des valeurs nulles
            if (aValue === null || aValue === undefined) aValue = '';
            if (bValue === null || bValue === undefined) bValue = '';

            // Conversion pour les nombres
            if (typeof aValue === 'string' && !isNaN(aValue)) aValue = parseFloat(aValue);
            if (typeof bValue === 'string' && !isNaN(bValue)) bValue = parseFloat(bValue);

            if (this.sortDirection === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
    }

    updateOrdersStatistics() {
        const count = this.filteredOrders.length;
        const totalValue = this.filteredOrders.reduce((sum, order) => sum + (order.total_cif_dzd || 0), 0);
        const avgValue = count > 0 ? totalValue / count : 0;
        const avgCoeff = count > 0 ?
            this.filteredOrders.reduce((sum, order) => sum + (order.landed_cost_coefficient || 0), 0) / count : 0;

        document.getElementById('filteredOrdersCount').textContent = count;
        document.getElementById('filteredTotalValue').textContent = this.formatCurrency(totalValue) + ' DZD';
        document.getElementById('filteredAvgValue').textContent = this.formatCurrency(avgValue) + ' DZD';
        document.getElementById('filteredAvgCoeff').textContent = avgCoeff.toFixed(4);
    }

    updatePagination() {
        const totalPages = Math.ceil(this.filteredOrders.length / this.itemsPerPage);
        const pagination = document.getElementById('ordersPagination');

        let paginationHTML = '';

        // Bouton précédent
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="app.goToPage(${this.currentPage - 1})">Précédent</a>
            </li>
        `;

        // Pages
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="app.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        // Bouton suivant
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="app.goToPage(${this.currentPage + 1})">Suivant</a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredOrders.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.updateOrdersTable();
        }
    }

    sortOrders(field) {
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }
        this.currentPage = 1; // Retour à la première page
        this.updateOrdersTable();
    }

    getOrderStatus(order) {
        if (order.actual_time_of_arrival) {
            return { text: 'Arrivé', color: 'success' };
        } else if (order.invoice_date) {
            return { text: 'En Transit', color: 'warning' };
        } else {
            return { text: 'En Préparation', color: 'info' };
        }
    }

    getCoefficientBadgeColor(coefficient) {
        if (!coefficient) return 'secondary';
        if (coefficient < 1.05) return 'success';
        if (coefficient < 1.15) return 'warning';
        return 'danger';
    }

    filterOrders() {
        const searchTerm = document.getElementById('searchOrders').value.toLowerCase();
        const shipmentType = document.getElementById('filterShipmentType').value;
        const dateFrom = document.getElementById('filterDateFrom').value;
        const dateTo = document.getElementById('filterDateTo').value;

        let filteredOrders = this.orders.filter(order => {
            // Filtre de recherche
            const matchesSearch = !searchTerm || 
                order.order_number.toLowerCase().includes(searchTerm) ||
                (order.supplier_name && order.supplier_name.toLowerCase().includes(searchTerm)) ||
                (order.from_location && order.from_location.toLowerCase().includes(searchTerm)) ||
                (order.to_location && order.to_location.toLowerCase().includes(searchTerm));

            // Filtre type d'expédition
            const matchesShipmentType = !shipmentType || order.shipment_type === shipmentType;

            // Filtre de date
            const orderDate = new Date(order.order_date);
            const matchesDateFrom = !dateFrom || orderDate >= new Date(dateFrom);
            const matchesDateTo = !dateTo || orderDate <= new Date(dateTo);

            return matchesSearch && matchesShipmentType && matchesDateFrom && matchesDateTo;
        });

        // Mettre à jour le tableau avec les résultats filtrés
        const tbody = document.getElementById('ordersTable');
        tbody.innerHTML = filteredOrders.map(order => `
            <tr>
                <td>${order.order_number}</td>
                <td>${this.formatDate(order.order_date)}</td>
                <td>${order.supplier_name || '-'}</td>
                <td><span class="badge bg-${this.getShipmentTypeBadgeColor(order.shipment_type)}">${order.shipment_type}</span></td>
                <td>${order.from_location || '-'}</td>
                <td>${order.to_location || '-'}</td>
                <td>${this.formatCurrency(order.total_cif_dzd)} DZD</td>
                <td>${order.items_count || 0}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="app.editOrder(${order.id})" title="Modifier">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="app.viewOrder(${order.id})" title="Voir détails">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.deleteOrder(${order.id})" title="Supprimer">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    clearFilters() {
        document.getElementById('searchOrders').value = '';
        document.getElementById('filterShipmentType').value = '';
        document.getElementById('filterDateFrom').value = '';
        document.getElementById('filterDateTo').value = '';
        this.updateOrdersTable();
    }

    resetOrderForm() {
        document.getElementById('orderForm').reset();
        document.getElementById('orderFormTitle').textContent = 'Nouvelle Commande';
        this.editingOrderId = null;
        this.orderItems = [];
        this.arrivalNotices = [];
        this.costAllocations = [];

        // Définir la date d'aujourd'hui par défaut
        document.getElementById('orderDate').value = new Date().toISOString().split('T')[0];

        // Réinitialiser les tableaux et conteneurs
        this.updateItemsTable();
        this.updateArrivalNoticesContainer();
        this.updateCostAllocationsContainer();
        this.updateCalculations();
    }

    async handleOrderSubmit() {
        try {
            // Validation du formulaire
            const validationErrors = this.validateOrderForm();
            if (validationErrors.length > 0) {
                this.showError('Erreurs de validation:\n' + validationErrors.join('\n'));
                return;
            }

            this.showLoading(true);

            const orderData = this.getOrderFormData();
            let result;

            if (this.editingOrderId) {
                result = await window.electronAPI.orders.update(this.editingOrderId, orderData);
            } else {
                result = await window.electronAPI.orders.create(orderData);
            }

            if (result.success) {
                this.showSuccess(this.editingOrderId ? 'Commande mise à jour avec succès' : 'Commande créée avec succès');

                // Supprimer le brouillon après sauvegarde réussie
                localStorage.removeItem('orderDraft');

                this.navigateToPage('orders');
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Erreur lors de l\'enregistrement: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    getOrderFormData() {
        return {
            order_number: document.getElementById('orderNumber').value,
            order_date: document.getElementById('orderDate').value,
            supplier_name: document.getElementById('supplierName').value,
            shipment_type: document.getElementById('shipmentType').value,
            from_location: document.getElementById('fromLocation').value,
            to_location: document.getElementById('toLocation').value,
            goods_description: document.getElementById('goodsDescription').value,

            // Informations financières
            fob_amount_currency: parseFloat(document.getElementById('fobAmountCurrency').value) || 0,
            fob_currency: document.getElementById('fobCurrency').value,
            freight_amount_currency: parseFloat(document.getElementById('freightAmountCurrency').value) || 0,
            exchange_rate_usd_dzd: parseFloat(document.getElementById('exchangeRateUsdDzd').value) || 0,
            exchange_rate_eur_dzd: parseFloat(document.getElementById('exchangeRateEurDzd').value) || 0,
            exchange_rate_yuan_dzd: parseFloat(document.getElementById('exchangeRateYuanDzd').value) || 0,

            // Informations logistiques et bancaires
            invoice_number: document.getElementById('invoiceNumber').value,
            invoice_date: document.getElementById('invoiceDate').value,
            bank_name: document.getElementById('bankName').value,
            lc_number: document.getElementById('lcNumber').value,
            lc_validation_date: document.getElementById('lcValidationDate').value,
            operation_type: document.getElementById('operationType').value,
            payment_term: document.getElementById('paymentTerm').value,
            price_term: document.getElementById('priceTerm').value,
            quantity_pcs: parseInt(document.getElementById('quantityPcs').value) || 0,
            num_containers_20ft: parseInt(document.getElementById('numContainers20ft').value) || 0,
            num_containers_40ft: parseInt(document.getElementById('numContainers40ft').value) || 0,
            num_packages: parseInt(document.getElementById('numPackages').value) || 0,

            // Données liées
            items: this.orderItems,
            arrivalNotices: this.arrivalNotices,
            costAllocations: this.costAllocations
        };
    }

    async editOrder(orderId) {
        try {
            this.showLoading(true);
            const result = await window.electronAPI.orders.getById(orderId);
            
            if (result.success) {
                this.editingOrderId = orderId;
                this.populateOrderForm(result.data);
                this.navigateToPage('new-order');
                document.getElementById('orderFormTitle').textContent = 'Modifier Commande';
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Erreur lors du chargement de la commande: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    populateOrderForm(order) {
        // Informations générales
        document.getElementById('orderNumber').value = order.order_number || '';
        document.getElementById('orderDate').value = order.order_date || '';
        document.getElementById('supplierName').value = order.supplier_name || '';
        document.getElementById('shipmentType').value = order.shipment_type || 'SEA';
        document.getElementById('fromLocation').value = order.from_location || '';
        document.getElementById('toLocation').value = order.to_location || '';
        document.getElementById('goodsDescription').value = order.goods_description || '';

        // Informations financières
        document.getElementById('fobAmountCurrency').value = order.fob_amount_currency || '';
        document.getElementById('fobCurrency').value = order.fob_currency || 'USD';
        document.getElementById('freightAmountCurrency').value = order.freight_amount_currency || '';
        document.getElementById('exchangeRateUsdDzd').value = order.exchange_rate_usd_dzd || '';
        document.getElementById('exchangeRateEurDzd').value = order.exchange_rate_eur_dzd || '';
        document.getElementById('exchangeRateYuanDzd').value = order.exchange_rate_yuan_dzd || '';

        // Informations logistiques et bancaires
        document.getElementById('invoiceNumber').value = order.invoice_number || '';
        document.getElementById('invoiceDate').value = order.invoice_date || '';
        document.getElementById('bankName').value = order.bank_name || '';
        document.getElementById('lcNumber').value = order.lc_number || '';
        document.getElementById('lcValidationDate').value = order.lc_validation_date || '';
        document.getElementById('operationType').value = order.operation_type || 'Import Commercial';
        document.getElementById('paymentTerm').value = order.payment_term || '';
        document.getElementById('priceTerm').value = order.price_term || '';
        document.getElementById('quantityPcs').value = order.quantity_pcs || '';
        document.getElementById('numContainers20ft').value = order.num_containers_20ft || '';
        document.getElementById('numContainers40ft').value = order.num_containers_40ft || '';
        document.getElementById('numPackages').value = order.num_packages || '';

        // Charger les données liées
        this.orderItems = order.items || [];
        this.arrivalNotices = order.arrivalNotices || [];
        this.costAllocations = order.costAllocations || [];

        // Mettre à jour l'affichage
        this.updateItemsTable();
        this.updateArrivalNoticesContainer();
        this.updateCostAllocationsContainer();
        this.updateCalculations();
    }

    async deleteOrder(orderId) {
        if (!confirm('Êtes-vous sûr de vouloir supprimer cette commande ?')) {
            return;
        }

        try {
            this.showLoading(true);
            const result = await window.electronAPI.orders.delete(orderId);
            
            if (result.success) {
                this.showSuccess('Commande supprimée avec succès');
                await this.loadPageData(this.currentPage);
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Erreur lors de la suppression: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    async viewOrder(orderId) {
        try {
            this.showLoading(true);
            const result = await window.electronAPI.orders.getById(orderId);

            if (result.success) {
                this.currentOrderDetail = result.data;
                this.populateOrderDetail(result.data);
                this.navigateToPage('orderDetail');
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Erreur lors du chargement des détails: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    populateOrderDetail(order) {
        // Informations générales
        document.getElementById('orderDetailTitle').textContent = `Détail de la Commande ${order.order_number}`;
        document.getElementById('detailOrderNumber').textContent = order.order_number;
        document.getElementById('detailOrderDate').textContent = this.formatDate(order.order_date);
        document.getElementById('detailSupplierName').textContent = order.supplier_name || '-';
        document.getElementById('detailShipmentType').innerHTML = `<span class="badge bg-${this.getShipmentTypeBadgeColor(order.shipment_type)}">${order.shipment_type}</span>`;
        document.getElementById('detailFromLocation').textContent = order.from_location || '-';
        document.getElementById('detailToLocation').textContent = order.to_location || '-';
        document.getElementById('detailGoodsDescription').textContent = order.goods_description || '-';

        // Résumé financier
        document.getElementById('detailFobAmount').textContent = this.formatCurrency(order.fob_amount_currency);
        document.getElementById('detailFobCurrency').textContent = order.fob_currency;
        document.getElementById('detailFobDzd').textContent = this.formatCurrency(order.fob_amount_dzd) + ' DZD';
        document.getElementById('detailFreightDzd').textContent = this.formatCurrency(order.freight_amount_dzd) + ' DZD';
        document.getElementById('detailCifDzd').textContent = this.formatCurrency(order.total_cif_dzd) + ' DZD';
        document.getElementById('detailLandedCost').textContent = this.formatCurrency(order.landed_cost_ht) + ' DZD';
        document.getElementById('detailCoefficient').textContent = (order.landed_cost_coefficient || 0).toFixed(4);
        document.getElementById('detailTotalTtc').textContent = this.formatCurrency(order.total_paid_ttc) + ' DZD';

        // Informations financières détaillées
        document.getElementById('detailBankName').textContent = order.bank_name || '-';
        document.getElementById('detailLcNumber').textContent = order.lc_number || '-';
        document.getElementById('detailLcValidationDate').textContent = this.formatDate(order.lc_validation_date);
        document.getElementById('detailPaymentTerm').textContent = order.payment_term || '-';
        document.getElementById('detailPriceTerm').textContent = order.price_term || '-';
        document.getElementById('detailOperationType').textContent = order.operation_type || '-';
        document.getElementById('detailRateUsd').textContent = order.exchange_rate_usd_dzd || '-';
        document.getElementById('detailRateEur').textContent = order.exchange_rate_eur_dzd || '-';
        document.getElementById('detailRateYuan').textContent = order.exchange_rate_yuan_dzd || '-';
        document.getElementById('detailQuantityPcs').textContent = order.quantity_pcs || '-';
        document.getElementById('detailContainers20').textContent = order.num_containers_20ft || '-';
        document.getElementById('detailContainers40').textContent = order.num_containers_40ft || '-';
        document.getElementById('detailPackages').textContent = order.num_packages || '-';
        document.getElementById('detailInvoiceNumber').textContent = order.invoice_number || '-';
        document.getElementById('detailInvoiceDate').textContent = this.formatDate(order.invoice_date);

        // Compter les éléments pour les onglets
        const itemsCount = order.items ? order.items.length : 0;
        const costsCount = order.costAllocations ? order.costAllocations.length : 0;
        const arrivalCount = order.arrivalNotices ? order.arrivalNotices.length : 0;

        document.getElementById('itemsCount').textContent = itemsCount;
        document.getElementById('costsCount').textContent = costsCount;
        document.getElementById('arrivalCount').textContent = arrivalCount;

        // Remplir les onglets
        this.populateDetailItems(order.items || []);
        this.populateDetailCosts(order.costAllocations || []);
        this.populateDetailArrival(order.arrivalNotices || []);
    }

    populateDetailItems(items) {
        const tbody = document.getElementById('detailItemsTable');
        tbody.innerHTML = items.map(item => `
            <tr>
                <td>${item.item_number}</td>
                <td>${item.part_number || '-'}</td>
                <td>${item.description || '-'}</td>
                <td>${item.quantity}</td>
                <td>${this.formatCurrency(item.unit_fob)}</td>
                <td>${this.formatCurrency(item.amount)}</td>
                <td>${this.formatCurrency(item.unit_fob_dzd)} DZD</td>
                <td>${this.formatCurrency(item.unit_cost_price)} DZD</td>
                <td>${this.formatCurrency(item.total_cost_price)} DZD</td>
                <td>
                    <span class="badge bg-${item.unit_cost_price - item.unit_fob_dzd > item.unit_fob_dzd * 0.1 ? 'danger' : 'success'}">
                        +${this.formatCurrency(item.unit_cost_price - item.unit_fob_dzd)} DZD
                    </span>
                </td>
            </tr>
        `).join('');
    }

    populateDetailCosts(costs) {
        const tbody = document.getElementById('detailCostsTable');
        tbody.innerHTML = costs.map(cost => `
            <tr>
                <td>${this.getCostTypeLabel(cost.cost_type)}</td>
                <td>${cost.cost_name || '-'}</td>
                <td>${cost.invoice_number || '-'}</td>
                <td>${this.formatDate(cost.invoice_date)}</td>
                <td>${this.formatCurrency(cost.ht)} DZD</td>
                <td>${this.formatCurrency(cost.tva)} DZD</td>
                <td>${this.formatCurrency(cost.total_ttc)} DZD</td>
                <td>
                    <span class="badge bg-${cost.is_manual_entry ? 'warning' : 'info'}">
                        ${cost.is_manual_entry ? 'Manuel' : 'Auto'}
                    </span>
                </td>
            </tr>
        `).join('');

        // Calculer les totaux par type
        const totals = {
            customs: costs.filter(c => c.cost_type === 'CustomsDuties1').reduce((sum, c) => sum + (c.ht || 0), 0),
            delivery: costs.filter(c => c.cost_type === 'ImportDelivery').reduce((sum, c) => sum + (c.ht || 0), 0),
            agency: costs.filter(c => c.cost_type === 'ShippingAgencyServices').reduce((sum, c) => sum + (c.ht || 0), 0),
            other: costs.filter(c => c.cost_type === 'OtherMiscellaneous').reduce((sum, c) => sum + (c.ht || 0), 0)
        };

        const totalHT = costs.reduce((sum, c) => sum + (c.ht || 0), 0);
        const totalTTC = costs.reduce((sum, c) => sum + (c.total_ttc || 0), 0);

        document.getElementById('totalCustomsHT').textContent = this.formatCurrency(totals.customs) + ' DZD';
        document.getElementById('totalDeliveryHT').textContent = this.formatCurrency(totals.delivery) + ' DZD';
        document.getElementById('totalAgencyHT').textContent = this.formatCurrency(totals.agency) + ' DZD';
        document.getElementById('totalOtherHT').textContent = this.formatCurrency(totals.other) + ' DZD';
        document.getElementById('grandTotalHT').textContent = this.formatCurrency(totalHT) + ' DZD';
        document.getElementById('grandTotalTTC').textContent = this.formatCurrency(totalTTC) + ' DZD';
    }

    populateDetailArrival(notices) {
        const container = document.getElementById('detailArrivalNotices');
        container.innerHTML = notices.map(notice => `
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-ship me-2"></i>${notice.vessel_name || 'Navire non spécifié'}</h6>
                            <p class="mb-1"><strong>Armateur:</strong> ${notice.shipowner || '-'}</p>
                            <p class="mb-1"><strong>N° Voyage:</strong> ${notice.voyage_number || '-'}</p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><strong>N° Connaissement:</strong> ${notice.bill_of_lading_number || '-'}</p>
                            <p class="mb-1"><strong>Date d'Arrivée:</strong>
                                <span class="badge bg-success">${this.formatDate(notice.actual_time_of_arrival)}</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        if (notices.length === 0) {
            container.innerHTML = '<div class="text-center text-muted py-4">Aucun avis d\'arrivée enregistré</div>';
        }
    }

    getCostTypeLabel(type) {
        const labels = {
            'CustomsDuties1': 'Droits de Douane',
            'ImportDelivery': 'Livraison Import',
            'ShippingAgencyServices': 'Services Agence Maritime',
            'OtherMiscellaneous': 'Autres Divers'
        };
        return labels[type] || type;
    }

    // === FONCTIONNALITÉS AVANCÉES ===
    toggleSelectAllOrders(checked) {
        document.querySelectorAll('.order-checkbox').forEach(checkbox => {
            checkbox.checked = checked;
        });
    }

    async exportFilteredOrders() {
        try {
            this.showLoading(true);
            const selectedIds = Array.from(document.querySelectorAll('.order-checkbox:checked')).map(cb => parseInt(cb.value));
            const ordersToExport = selectedIds.length > 0 ?
                this.orders.filter(order => selectedIds.includes(order.id)) :
                this.filteredOrders;

            const result = await window.electronAPI.excel.export({ orders: ordersToExport });

            if (result.success) {
                this.showSuccess(`Export réussi: ${ordersToExport.length} commandes exportées`);
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Erreur lors de l\'export: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    async exportOrderItems() {
        if (!this.currentOrderDetail) return;

        try {
            this.showLoading(true);
            const result = await window.electronAPI.excel.export({
                orders: [this.currentOrderDetail],
                exportType: 'items_only'
            });

            if (result.success) {
                this.showSuccess('Export des articles réussi');
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Erreur lors de l\'export des articles: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    // Validation avancée des formulaires
    validateOrderForm() {
        const errors = [];

        // Validation du numéro de commande
        const orderNumber = document.getElementById('orderNumber').value.trim();
        if (!orderNumber) {
            errors.push('Le numéro de commande est obligatoire');
        }

        // Validation des montants
        const fobAmount = parseFloat(document.getElementById('fobAmountCurrency').value);
        if (!fobAmount || fobAmount <= 0) {
            errors.push('Le montant FOB doit être supérieur à 0');
        }

        // Validation des taux de change
        const currency = document.getElementById('fobCurrency').value;
        let exchangeRate = 0;
        switch (currency) {
            case 'USD':
                exchangeRate = parseFloat(document.getElementById('exchangeRateUsdDzd').value);
                break;
            case 'EUR':
                exchangeRate = parseFloat(document.getElementById('exchangeRateEurDzd').value);
                break;
            case 'CNY':
                exchangeRate = parseFloat(document.getElementById('exchangeRateYuanDzd').value);
                break;
        }

        if (!exchangeRate || exchangeRate <= 0) {
            errors.push(`Le taux de change ${currency}/DZD est obligatoire et doit être supérieur à 0`);
        }

        // Validation des articles
        if (this.orderItems.length === 0) {
            errors.push('Au moins un article doit être ajouté à la commande');
        }

        // Validation des quantités d'articles
        for (const item of this.orderItems) {
            if (!item.quantity || item.quantity <= 0) {
                errors.push(`La quantité de l'article "${item.description || item.part_number}" doit être supérieure à 0`);
            }
            if (!item.unit_fob || item.unit_fob <= 0) {
                errors.push(`Le prix unitaire FOB de l'article "${item.description || item.part_number}" doit être supérieur à 0`);
            }
        }

        return errors;
    }

    // Sauvegarde automatique (brouillon)
    saveOrderDraft() {
        const draftData = this.getOrderFormData();
        localStorage.setItem('orderDraft', JSON.stringify(draftData));
        this.showInfo('Brouillon sauvegardé automatiquement');
    }

    loadOrderDraft() {
        const draft = localStorage.getItem('orderDraft');
        if (draft && confirm('Un brouillon de commande a été trouvé. Voulez-vous le charger ?')) {
            try {
                const draftData = JSON.parse(draft);
                this.populateOrderForm(draftData);
                localStorage.removeItem('orderDraft');
                this.showInfo('Brouillon chargé avec succès');
            } catch (error) {
                this.showError('Erreur lors du chargement du brouillon');
                localStorage.removeItem('orderDraft');
            }
        }
    }

    // Recherche avancée
    setupAdvancedSearch() {
        const searchInput = document.getElementById('searchOrders');
        let searchTimeout;

        searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.currentPage = 1;
                this.updateOrdersTable();
            }, 300); // Délai de 300ms pour éviter trop de requêtes
        });
    }

    // Raccourcis clavier
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+N : Nouvelle commande
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                this.navigateToPage('new-order');
            }

            // Ctrl+S : Sauvegarder (si on est sur le formulaire)
            if (e.ctrlKey && e.key === 's' && this.currentPage === 'new-order') {
                e.preventDefault();
                document.getElementById('orderForm').dispatchEvent(new Event('submit'));
            }

            // Échap : Retour à la liste
            if (e.key === 'Escape' && (this.currentPage === 'new-order' || this.currentPage === 'orderDetail')) {
                this.navigateToPage('orders');
            }

            // F5 : Actualiser
            if (e.key === 'F5') {
                e.preventDefault();
                this.loadPageData(this.currentPage);
            }
        });
    }

    async handleImportExcel() {
        try {
            this.showLoading(true);
            const result = await window.electronAPI.excel.import();
            
            if (result.success) {
                this.showSuccess(`Import réussi: ${result.data.importedRows} commandes importées sur ${result.data.totalRows} lignes`);
                await this.loadPageData(this.currentPage);
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Erreur lors de l\'import: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    async handleExportExcel() {
        try {
            this.showLoading(true);
            const result = await window.electronAPI.excel.export({ orders: this.orders });
            
            if (result.success) {
                this.showSuccess(`Export réussi: ${result.exportedOrders} commandes exportées vers ${result.filePath}`);
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Erreur lors de l\'export: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    // === GESTION DES ARTICLES ===
    addOrderItem() {
        const newItem = {
            id: Date.now(), // ID temporaire
            item_number: this.orderItems.length + 1,
            part_number: '',
            description: '',
            quantity: 0,
            unit_fob: 0,
            amount: 0
        };
        this.orderItems.push(newItem);
        this.updateItemsTable();
    }

    removeOrderItem(index) {
        this.orderItems.splice(index, 1);
        // Renuméroter les articles
        this.orderItems.forEach((item, i) => {
            item.item_number = i + 1;
        });
        this.updateItemsTable();
        this.updateCalculations();
    }

    updateItemsTable() {
        const tbody = document.getElementById('itemsTableBody');
        tbody.innerHTML = this.orderItems.map((item, index) => `
            <tr>
                <td>${item.item_number}</td>
                <td>
                    <input type="text" class="form-control form-control-sm"
                           value="${item.part_number}"
                           onchange="app.updateItemField(${index}, 'part_number', this.value)">
                </td>
                <td>
                    <input type="text" class="form-control form-control-sm"
                           value="${item.description}"
                           onchange="app.updateItemField(${index}, 'description', this.value)">
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm"
                           value="${item.quantity}"
                           onchange="app.updateItemField(${index}, 'quantity', parseFloat(this.value) || 0)">
                </td>
                <td>
                    <input type="number" step="0.01" class="form-control form-control-sm"
                           value="${item.unit_fob}"
                           onchange="app.updateItemField(${index}, 'unit_fob', parseFloat(this.value) || 0)">
                </td>
                <td>
                    <span class="fw-bold">${this.formatCurrency(item.amount)}</span>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-danger"
                            onclick="app.removeOrderItem(${index})">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    updateItemField(index, field, value) {
        if (this.orderItems[index]) {
            this.orderItems[index][field] = value;

            // Recalculer le montant si quantité ou prix unitaire change
            if (field === 'quantity' || field === 'unit_fob') {
                const item = this.orderItems[index];
                item.amount = (item.quantity || 0) * (item.unit_fob || 0);
                this.updateItemsTable();
                this.updateCalculations();
            }
        }
    }

    // === GESTION DES AVIS D'ARRIVÉE ===
    addArrivalNotice() {
        const newNotice = {
            id: Date.now(),
            voyage_number: '',
            bill_of_lading_number: '',
            vessel_name: '',
            shipowner: '',
            actual_time_of_arrival: ''
        };
        this.arrivalNotices.push(newNotice);
        this.updateArrivalNoticesContainer();
    }

    removeArrivalNotice(index) {
        this.arrivalNotices.splice(index, 1);
        this.updateArrivalNoticesContainer();
    }

    updateArrivalNoticesContainer() {
        const container = document.getElementById('arrivalNoticesContainer');
        container.innerHTML = this.arrivalNotices.map((notice, index) => `
            <div class="card mb-2">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-2">
                                <label class="form-label">Numéro de Voyage</label>
                                <input type="text" class="form-control form-control-sm"
                                       value="${notice.voyage_number}"
                                       onchange="app.updateArrivalNoticeField(${index}, 'voyage_number', this.value)">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-2">
                                <label class="form-label">Numéro de Connaissement</label>
                                <input type="text" class="form-control form-control-sm"
                                       value="${notice.bill_of_lading_number}"
                                       onchange="app.updateArrivalNoticeField(${index}, 'bill_of_lading_number', this.value)">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-2">
                                <label class="form-label">Nom du Navire</label>
                                <input type="text" class="form-control form-control-sm"
                                       value="${notice.vessel_name}"
                                       onchange="app.updateArrivalNoticeField(${index}, 'vessel_name', this.value)">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-2">
                                <label class="form-label">Armateur</label>
                                <input type="text" class="form-control form-control-sm"
                                       value="${notice.shipowner}"
                                       onchange="app.updateArrivalNoticeField(${index}, 'shipowner', this.value)">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-2">
                                <label class="form-label">Date d'Arrivée</label>
                                <div class="input-group">
                                    <input type="date" class="form-control form-control-sm"
                                           value="${notice.actual_time_of_arrival}"
                                           onchange="app.updateArrivalNoticeField(${index}, 'actual_time_of_arrival', this.value)">
                                    <button type="button" class="btn btn-outline-danger btn-sm"
                                            onclick="app.removeArrivalNotice(${index})">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    updateArrivalNoticeField(index, field, value) {
        if (this.arrivalNotices[index]) {
            this.arrivalNotices[index][field] = value;
        }
    }

    // === GESTION DES ALLOCATIONS DE COÛTS ===
    addCostAllocation() {
        const newCost = {
            id: Date.now(),
            cost_type: 'CustomsDuties1',
            cost_name: '',
            invoice_number: '',
            invoice_date: '',
            total_ttc: 0,
            tva: 0,
            ht: 0,
            is_manual_entry: false
        };
        this.costAllocations.push(newCost);
        this.updateCostAllocationsContainer();
    }

    removeCostAllocation(index) {
        this.costAllocations.splice(index, 1);
        this.updateCostAllocationsContainer();
        this.updateCalculations();
    }

    updateCostAllocationsContainer() {
        const container = document.getElementById('costAllocationsContainer');
        container.innerHTML = this.costAllocations.map((cost, index) => `
            <div class="card mb-2">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-2">
                                <label class="form-label">Type de Coût</label>
                                <select class="form-select form-select-sm"
                                        onchange="app.updateCostAllocationField(${index}, 'cost_type', this.value)">
                                    <option value="CustomsDuties1" ${cost.cost_type === 'CustomsDuties1' ? 'selected' : ''}>Droits de Douane</option>
                                    <option value="ImportDelivery" ${cost.cost_type === 'ImportDelivery' ? 'selected' : ''}>Livraison Import</option>
                                    <option value="ShippingAgencyServices" ${cost.cost_type === 'ShippingAgencyServices' ? 'selected' : ''}>Services Agence Maritime</option>
                                    <option value="OtherMiscellaneous" ${cost.cost_type === 'OtherMiscellaneous' ? 'selected' : ''}>Autres Divers</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-2">
                                <label class="form-label">Nom du Coût</label>
                                <input type="text" class="form-control form-control-sm"
                                       value="${cost.cost_name}"
                                       onchange="app.updateCostAllocationField(${index}, 'cost_name', this.value)">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-2">
                                <label class="form-label">N° Facture</label>
                                <input type="text" class="form-control form-control-sm"
                                       value="${cost.invoice_number}"
                                       onchange="app.updateCostAllocationField(${index}, 'invoice_number', this.value)">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-2">
                                <label class="form-label">Date Facture</label>
                                <input type="date" class="form-control form-control-sm"
                                       value="${cost.invoice_date}"
                                       onchange="app.updateCostAllocationField(${index}, 'invoice_date', this.value)">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-2">
                                <label class="form-label">Total TTC</label>
                                <input type="number" step="0.01" class="form-control form-control-sm"
                                       value="${cost.total_ttc}"
                                       onchange="app.updateCostAllocationField(${index}, 'total_ttc', parseFloat(this.value) || 0)">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-2">
                                <label class="form-label">TVA</label>
                                <input type="number" step="0.01" class="form-control form-control-sm"
                                       value="${cost.tva}"
                                       onchange="app.updateCostAllocationField(${index}, 'tva', parseFloat(this.value) || 0)">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-2">
                                <label class="form-label">HT</label>
                                <input type="number" step="0.01" class="form-control form-control-sm"
                                       value="${cost.ht}"
                                       onchange="app.updateCostAllocationField(${index}, 'ht', parseFloat(this.value) || 0)">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-2">
                                <label class="form-label">Actions</label>
                                <div class="d-flex gap-1">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               ${cost.is_manual_entry ? 'checked' : ''}
                                               onchange="app.updateCostAllocationField(${index}, 'is_manual_entry', this.checked)">
                                        <label class="form-check-label">Manuel</label>
                                    </div>
                                    <button type="button" class="btn btn-outline-danger btn-sm"
                                            onclick="app.removeCostAllocation(${index})">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    updateCostAllocationField(index, field, value) {
        if (this.costAllocations[index]) {
            this.costAllocations[index][field] = value;

            // Recalculer automatiquement HT si TTC et TVA changent
            if (field === 'total_ttc' || field === 'tva') {
                const cost = this.costAllocations[index];
                if (cost.total_ttc && cost.tva) {
                    cost.ht = cost.total_ttc - cost.tva;
                } else if (cost.total_ttc && !cost.tva) {
                    // Supposer 19% de TVA par défaut
                    cost.tva = cost.total_ttc * 0.19 / 1.19;
                    cost.ht = cost.total_ttc - cost.tva;
                }
                this.updateCostAllocationsContainer();
            }

            this.updateCalculations();
        }
    }

    // === CALCULS EN TEMPS RÉEL ===
    updateCalculations() {
        const fobAmount = parseFloat(document.getElementById('fobAmountCurrency')?.value) || 0;
        const freightAmount = parseFloat(document.getElementById('freightAmountCurrency')?.value) || 0;
        const currency = document.getElementById('fobCurrency')?.value || 'USD';

        // Obtenir le taux de change
        let exchangeRate = 1;
        switch (currency) {
            case 'USD':
                exchangeRate = parseFloat(document.getElementById('exchangeRateUsdDzd')?.value) || 1;
                break;
            case 'EUR':
                exchangeRate = parseFloat(document.getElementById('exchangeRateEurDzd')?.value) || 1;
                break;
            case 'CNY':
                exchangeRate = parseFloat(document.getElementById('exchangeRateYuanDzd')?.value) || 1;
                break;
        }

        // Calculs de base
        const fobDzd = fobAmount * exchangeRate;
        const freightDzd = freightAmount * exchangeRate;
        const cifDzd = fobDzd + freightDzd;

        // Total des allocations
        const totalAllocationsHT = this.costAllocations.reduce((sum, cost) => sum + (cost.ht || 0), 0);
        const totalAllocationsTTC = this.costAllocations.reduce((sum, cost) => sum + (cost.total_ttc || 0), 0);

        // Coût d'atterrissage
        const landedCost = cifDzd + totalAllocationsHT;
        const coefficient = cifDzd > 0 ? landedCost / cifDzd : 0;
        const totalTtc = cifDzd + totalAllocationsTTC;

        // Mettre à jour l'affichage
        document.getElementById('calculatedCifDzd').textContent = this.formatCurrency(cifDzd) + ' DZD';
        document.getElementById('calculatedLandedCost').textContent = this.formatCurrency(landedCost) + ' DZD';
        document.getElementById('calculatedCoefficient').textContent = coefficient.toFixed(4);
        document.getElementById('calculatedTotalTtc').textContent = this.formatCurrency(totalTtc) + ' DZD';
    }

    // === GESTION DES RAPPORTS ===
    async loadReportsData() {
        // Charger les données initiales des rapports
        await this.generateReports();
    }

    async generateReports() {
        try {
            this.showLoading(true);

            const filters = {
                dateFrom: document.getElementById('reportDateFrom').value,
                dateTo: document.getElementById('reportDateTo').value,
                shipmentType: document.getElementById('reportShipmentType').value,
                supplier: document.getElementById('reportSupplier').value
            };

            // Générer tous les rapports
            await Promise.all([
                this.generateTrackingReport(filters),
                this.generateCostAnalysisReport(),
                this.generateMarginAnalysisReport(),
                this.generateCharts()
            ]);

        } catch (error) {
            this.showError('Erreur lors de la génération des rapports: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    async generateTrackingReport(filters) {
        const result = await window.electronAPI.reports.getOrderTracking(filters);
        if (result.success) {
            this.updateTrackingReportTable(result.data);
        }
    }

    async generateCostAnalysisReport() {
        const result = await window.electronAPI.reports.getCostAnalysis();
        if (result.success) {
            this.updateCostAnalysisTable(result.data);
        }
    }

    async generateMarginAnalysisReport() {
        const result = await window.electronAPI.reports.getMarginAnalysis();
        if (result.success) {
            this.updateMarginAnalysisTable(result.data);
        }
    }

    updateTrackingReportTable(data) {
        const tbody = document.getElementById('trackingReportBody');
        tbody.innerHTML = data.map(order => `
            <tr>
                <td>${order.order_number}</td>
                <td>${this.formatDate(order.order_date)}</td>
                <td>${order.supplier_name || '-'}</td>
                <td><span class="badge bg-${this.getShipmentTypeBadgeColor(order.shipment_type)}">${order.shipment_type}</span></td>
                <td><span class="badge bg-${this.getStatusBadgeColor(order.status)}">${order.status}</span></td>
                <td>${this.formatCurrency(order.total_cif_dzd)} DZD</td>
                <td>${this.formatCurrency(order.landed_cost_ht)} DZD</td>
                <td>${(order.landed_cost_coefficient || 0).toFixed(4)}</td>
            </tr>
        `).join('');
    }

    updateCostAnalysisTable(data) {
        const tbody = document.getElementById('costsReportBody');
        tbody.innerHTML = data.map(order => `
            <tr>
                <td>${order.order_number}</td>
                <td>${this.formatCurrency(order.fob_amount_dzd)}</td>
                <td>${this.formatCurrency(order.freight_amount_dzd)}</td>
                <td>${this.formatCurrency(order.total_cif_dzd)}</td>
                <td>${this.formatCurrency(order.customs_duties_ht)}</td>
                <td>${this.formatCurrency(order.import_delivery_ht)}</td>
                <td>${this.formatCurrency(order.shipping_agency_ht)}</td>
                <td>${this.formatCurrency(order.other_costs_ht)}</td>
                <td>${this.formatCurrency(order.total_allocations_ht)}</td>
                <td>${this.formatCurrency(order.landed_cost_ht)}</td>
            </tr>
        `).join('');
    }

    updateMarginAnalysisTable(data) {
        const tbody = document.getElementById('marginsReportBody');
        tbody.innerHTML = data.map(item => `
            <tr>
                <td>${item.order_number}</td>
                <td>${item.part_number || '-'}</td>
                <td>${item.description || '-'}</td>
                <td>${item.quantity}</td>
                <td>${this.formatCurrency(item.unit_fob_dzd)}</td>
                <td>${this.formatCurrency(item.unit_cost_price)}</td>
                <td>${this.formatCurrency(item.unit_additional_cost)}</td>
                <td><span class="badge bg-${item.cost_increase_percentage > 20 ? 'danger' : item.cost_increase_percentage > 10 ? 'warning' : 'success'}">${(item.cost_increase_percentage || 0).toFixed(2)}%</span></td>
            </tr>
        `).join('');
    }

    clearReportFilters() {
        document.getElementById('reportDateFrom').value = '';
        document.getElementById('reportDateTo').value = '';
        document.getElementById('reportShipmentType').value = '';
        document.getElementById('reportSupplier').value = '';
    }

    getStatusBadgeColor(status) {
        switch (status) {
            case 'Arrivé': return 'success';
            case 'En Transit': return 'warning';
            case 'En Préparation': return 'info';
            default: return 'secondary';
        }
    }

    // === GÉNÉRATION DES GRAPHIQUES ===
    async generateCharts() {
        await Promise.all([
            this.generateShipmentTypeChart(),
            this.generateTopSuppliersChart(),
            this.generateMonthlyEvolutionChart()
        ]);
    }

    async generateShipmentTypeChart() {
        try {
            const result = await window.electronAPI.reports.getChartData('costsByShipmentType');
            if (result.success) {
                const ctx = document.getElementById('shipmentTypeChart').getContext('2d');

                // Détruire le graphique existant s'il existe
                if (this.charts.shipmentType) {
                    this.charts.shipmentType.destroy();
                }

                this.charts.shipmentType = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: result.data.map(item => item.shipment_type),
                        datasets: [{
                            data: result.data.map(item => item.total_cif_value),
                            backgroundColor: [
                                '#0d6efd', '#198754', '#ffc107', '#dc3545', '#6f42c1'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: (context) => {
                                        const value = this.formatCurrency(context.raw);
                                        return `${context.label}: ${value} DZD`;
                                    }
                                }
                            }
                        }
                    }
                });
            }
        } catch (error) {
            console.error('Erreur génération graphique types expédition:', error);
        }
    }

    async generateTopSuppliersChart() {
        try {
            const result = await window.electronAPI.reports.getTopSuppliers(5);
            if (result.success) {
                const ctx = document.getElementById('topSuppliersChart').getContext('2d');

                if (this.charts.topSuppliers) {
                    this.charts.topSuppliers.destroy();
                }

                this.charts.topSuppliers = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: result.data.map(item => item.supplier_name),
                        datasets: [{
                            label: 'Valeur Totale (DZD)',
                            data: result.data.map(item => item.total_value),
                            backgroundColor: '#0d6efd',
                            borderColor: '#0a58ca',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: (value) => this.formatCurrency(value)
                                }
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: (context) => {
                                        const value = this.formatCurrency(context.raw);
                                        return `Valeur: ${value} DZD`;
                                    }
                                }
                            }
                        }
                    }
                });
            }
        } catch (error) {
            console.error('Erreur génération graphique top fournisseurs:', error);
        }
    }

    async generateMonthlyEvolutionChart() {
        try {
            const result = await window.electronAPI.reports.getCostEvolution('month');
            if (result.success) {
                const ctx = document.getElementById('monthlyEvolutionChart').getContext('2d');

                if (this.charts.monthlyEvolution) {
                    this.charts.monthlyEvolution.destroy();
                }

                this.charts.monthlyEvolution = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: result.data.map(item => item.period).reverse(),
                        datasets: [
                            {
                                label: 'CIF Total (DZD)',
                                data: result.data.map(item => item.total_cif).reverse(),
                                borderColor: '#0d6efd',
                                backgroundColor: 'rgba(13, 110, 253, 0.1)',
                                tension: 0.4
                            },
                            {
                                label: 'Coût d\'Atterrissage (DZD)',
                                data: result.data.map(item => item.total_landed_cost).reverse(),
                                borderColor: '#198754',
                                backgroundColor: 'rgba(25, 135, 84, 0.1)',
                                tension: 0.4
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: (value) => this.formatCurrency(value)
                                }
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: (context) => {
                                        const value = this.formatCurrency(context.raw);
                                        return `${context.dataset.label}: ${value} DZD`;
                                    }
                                }
                            }
                        }
                    }
                });
            }
        } catch (error) {
            console.error('Erreur génération graphique évolution mensuelle:', error);
        }
    }

    // Méthodes utilitaires
    formatDate(dateString) {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleDateString('fr-FR');
    }

    formatCurrency(amount) {
        if (!amount) return '0';
        return new Intl.NumberFormat('fr-FR', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }

    getShipmentTypeBadgeColor(type) {
        switch (type) {
            case 'SEA': return 'primary';
            case 'AIR': return 'info';
            case 'Express': return 'warning';
            default: return 'secondary';
        }
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (show) {
            overlay.classList.remove('d-none');
        } else {
            overlay.classList.add('d-none');
        }
    }

    showSuccess(message) {
        this.showAlert(message, 'success');
    }

    showError(message) {
        this.showAlert(message, 'danger');
    }

    showInfo(message) {
        this.showAlert(message, 'info');
    }

    showAlert(message, type) {
        // Créer une alerte temporaire
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // Supprimer automatiquement après 5 secondes
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }
}

// Initialiser l'application
const app = new ImportManagementApp();

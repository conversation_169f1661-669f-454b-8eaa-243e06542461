# Guide d'Utilisation - Application de Gestion des Importations

## 🚀 Démarrage Rapide

### 1. Installation et Lancement
```bash
# Installer les dépendances (première fois seulement)
npm install

# Lancer l'application en mode développement
npm run dev

# Ou lancer l'application normale
npm start
```

### 2. Première Connexion
- **Utilisateur par défaut** : `admin`
- **Mot de passe par défaut** : `admin123`
- Cliquez sur "Se connecter"

## 📋 Fonctionnalités Principales

### 🏠 Tableau de Bord
Le tableau de bord affiche :
- **Statistiques générales** : Nombre total de commandes, valeur totale, commandes du mois
- **Commandes récentes** : Les 5 dernières commandes avec actions rapides
- **Indicateurs visuels** : Cartes colorées pour un aperçu rapide

### 📦 Gestion des Commandes

#### Créer une Nouvelle Commande
1. C<PERSON>z sur "Nouvelle commande" dans la sidebar ou le bouton du tableau de bord
2. Remplissez les **Informations Générales** :
   - Num<PERSON>ro de commande (obligatoire)
   - Date de commande
   - Nom du fournisseur
   - Type d'expédition (Maritime, Aérien, Express, Autre)
   - Origine et destination
   - Description des marchandises

3. Complétez les **Informations Financières** :
   - Montant FOB et devise (USD, EUR, Yuan)
   - Montant du fret
   - Taux de change vers DZD
   - Les calculs se font automatiquement

4. Cliquez sur "Enregistrer"

#### Modifier une Commande
1. Dans la liste des commandes, cliquez sur l'icône crayon (✏️)
2. Modifiez les informations nécessaires
3. Cliquez sur "Enregistrer"

#### Supprimer une Commande
1. Cliquez sur l'icône poubelle (🗑️)
2. Confirmez la suppression

### 🔍 Recherche et Filtrage
Dans la page "Commandes" :
- **Recherche textuelle** : Tapez dans le champ de recherche pour filtrer par numéro, fournisseur, origine ou destination
- **Filtre par type d'expédition** : Sélectionnez un type spécifique
- **Filtre par dates** : Définissez une plage de dates
- **Effacer filtres** : Bouton pour réinitialiser tous les filtres

### 📊 Import/Export Excel

#### Import depuis Excel
1. Allez dans "Import/Export"
2. Cliquez sur "Sélectionner fichier Excel"
3. Choisissez votre fichier (.xlsx ou .xls)
4. L'application détecte automatiquement les colonnes
5. Vérifiez le résultat de l'import

**Format Excel attendu :**
- Numéro Commande
- Date Commande
- Fournisseur
- Type Expédition
- Origine / Destination
- Montant FOB / Devise
- Taux de change

#### Export vers Excel
1. Cliquez sur "Exporter vers Excel"
2. Choisissez l'emplacement de sauvegarde
3. Le fichier contient toutes les commandes avec calculs

## 🧮 Calculs Automatiques

L'application effectue automatiquement :

### Conversions de Devises
- **FOB DZD** = FOB (devise) × Taux de change
- **Fret DZD** = Fret (devise) × Taux de change
- **CIF DZD** = CIF (devise) × Taux de change

### Coûts d'Atterrissage
- **Coût d'atterrissage HT** = CIF DZD + Total allocations HT
- **Coefficient** = Coût d'atterrissage / FOB DZD
- **Total payé TTC** = CIF DZD + Total allocations TTC

## 🔐 Gestion des Utilisateurs

### Changer de Mot de Passe
*Fonctionnalité à implémenter dans une version future*

### Créer de Nouveaux Utilisateurs
*Fonctionnalité à implémenter dans une version future*

## 📁 Structure des Données

### Informations de Commande
- **Générales** : Numéro, dates, fournisseur, type d'expédition
- **Logistique** : Origine, destination, description des marchandises
- **Financières** : Montants FOB, fret, CIF, taux de change
- **Bancaires** : Banque, numéro LC, conditions de paiement

### Articles de Commande
- Numéro d'article, référence, description
- Quantité, prix unitaire FOB, montant total

### Avis d'Arrivée
- Numéro de voyage, connaissement
- Nom du navire, armateur, date d'arrivée

### Allocations de Coûts
- Types : Droits de douane, livraison, agence maritime, divers
- Montants HT, TVA, TTC
- Saisie manuelle pour cas spéciaux (ex: surestaries)

## 🛠️ Dépannage

### Problèmes Courants

#### L'application ne se lance pas
```bash
# Vérifier Node.js
node --version

# Réinstaller les dépendances
rm -rf node_modules
npm install
```

#### Erreurs de base de données
- La base de données se crée automatiquement au premier lancement
- Fichier : `data/import_management.db`
- En cas de corruption, supprimez le fichier pour recréer

#### Problèmes d'import Excel
- Vérifiez le format du fichier (.xlsx ou .xls)
- Assurez-vous que la première ligne contient les en-têtes
- Les colonnes peuvent être dans n'importe quel ordre
- Les noms de colonnes sont détectés automatiquement

### Messages d'Erreur

#### "Numéro de commande déjà existant"
- Chaque commande doit avoir un numéro unique
- Modifiez le numéro ou vérifiez les doublons

#### "Erreur de connexion à la base de données"
- Vérifiez les permissions du dossier `data/`
- Redémarrez l'application

## 📞 Support

### Logs et Débogage
- Mode développement : `npm run dev` (ouvre les DevTools)
- Logs dans la console du navigateur (F12)
- Fichiers de log dans le dossier de l'application

### Sauvegarde des Données
- **Base de données** : `data/import_management.db`
- **Export Excel** : Sauvegarde complète des données
- **Recommandation** : Export régulier vers Excel

### Performance
- L'application est optimisée pour des milliers de commandes
- Utilisez les filtres pour de gros volumes de données
- La recherche est indexée pour de meilleures performances

## 🔄 Mises à Jour

### Nouvelles Versions
```bash
# Sauvegarder les données
npm run export-data

# Mettre à jour l'application
git pull origin main
npm install

# Redémarrer
npm start
```

### Migration de Données
- Les migrations de base de données sont automatiques
- Sauvegardez toujours avant une mise à jour majeure

## 💡 Conseils d'Utilisation

### Bonnes Pratiques
1. **Numérotation** : Utilisez un système cohérent pour les numéros de commande
2. **Saisie** : Remplissez tous les champs importants pour de meilleurs rapports
3. **Sauvegarde** : Exportez régulièrement vers Excel
4. **Taux de change** : Mettez à jour régulièrement les taux

### Raccourcis Clavier
- **Ctrl+N** : Nouvelle commande *(à implémenter)*
- **Ctrl+S** : Sauvegarder *(à implémenter)*
- **Ctrl+F** : Rechercher *(à implémenter)*

### Optimisation
- Utilisez les filtres pour naviguer rapidement
- Les calculs sont automatiques, pas besoin de recalculer
- L'interface est responsive, adaptée aux différentes tailles d'écran

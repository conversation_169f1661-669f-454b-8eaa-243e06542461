const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class Database {
  constructor() {
    this.db = null;
    this.dbPath = path.join(__dirname, '../../data/import_management.db');
  }

  async initialize() {
    // C<PERSON>er le dossier data s'il n'existe pas
    const dataDir = path.dirname(this.dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('Erreur lors de l\'ouverture de la base de données:', err);
          reject(err);
        } else {
          console.log('Base de données SQLite connectée');
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  async createTables() {
    const schema = `
      -- Table des utilisateurs
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        role TEXT DEFAULT 'user',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Table des commandes principales
      CREATE TABLE IF NOT EXISTS orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_number TEXT UNIQUE NOT NULL,
        order_date DATE,
        supplier_name TEXT,
        shipment_type TEXT CHECK(shipment_type IN ('SEA', 'AIR', 'Express', 'Other')),
        invoice_number TEXT,
        invoice_date DATE,
        from_location TEXT,
        to_location TEXT,
        operation_type TEXT DEFAULT 'Import Commercial',
        goods_description TEXT,
        bank_name TEXT,
        lc_number TEXT,
        lc_validation_date DATE,
        payment_term TEXT,
        price_term TEXT,
        quantity_pcs INTEGER DEFAULT 0,
        num_containers_20ft INTEGER DEFAULT 0,
        num_containers_40ft INTEGER DEFAULT 0,
        num_packages INTEGER,
        exchange_rate_usd_dzd REAL DEFAULT 0,
        exchange_rate_eur_dzd REAL DEFAULT 0,
        exchange_rate_yuan_dzd REAL,
        fob_amount_currency REAL DEFAULT 0,
        fob_currency TEXT DEFAULT 'USD',
        freight_amount_currency REAL DEFAULT 0,
        total_cif_currency REAL DEFAULT 0,
        fob_amount_dzd REAL DEFAULT 0,
        freight_amount_dzd REAL DEFAULT 0,
        total_cif_dzd REAL DEFAULT 0,
        landed_cost_ht REAL DEFAULT 0,
        landed_cost_coefficient REAL DEFAULT 0,
        total_paid_ttc REAL DEFAULT 0,
        cost_price_order REAL DEFAULT 0,
        total_cost_allocations_ht REAL DEFAULT 0,
        total_cost_allocations_ttc REAL DEFAULT 0,
        items_count INTEGER DEFAULT 0,
        status TEXT DEFAULT 'En Préparation',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Table des articles de commande
      CREATE TABLE IF NOT EXISTS order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        item_number INTEGER,
        part_number TEXT,
        description TEXT,
        quantity INTEGER,
        unit_fob REAL,
        amount REAL,
        unit_fob_dzd REAL,
        unit_cost_price REAL,
        total_cost_price REAL,
        amount_fob_dzd REAL,
        FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE
      );

      -- Table des avis d'arrivée
      CREATE TABLE IF NOT EXISTS arrival_notices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        voyage_number TEXT,
        bill_of_lading_number TEXT,
        vessel_name TEXT,
        shipowner TEXT,
        actual_time_of_arrival DATE,
        FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE
      );

      -- Table des allocations de coûts
      CREATE TABLE IF NOT EXISTS cost_allocations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        cost_type TEXT,
        cost_name TEXT,
        invoice_number TEXT,
        invoice_date DATE,
        total_ttc REAL,
        tva REAL,
        ht REAL,
        is_manual_entry BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE
      );

      -- Index pour améliorer les performances
      CREATE INDEX IF NOT EXISTS idx_orders_number ON orders(order_number);
      CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
      CREATE INDEX IF NOT EXISTS idx_arrival_notices_order_id ON arrival_notices(order_id);
      CREATE INDEX IF NOT EXISTS idx_cost_allocations_order_id ON cost_allocations(order_id);
    `;

    return new Promise((resolve, reject) => {
      this.db.exec(schema, (err) => {
        if (err) {
          console.error('Erreur lors de la création des tables:', err);
          reject(err);
        } else {
          console.log('Tables créées avec succès');
          this.createDefaultUser().then(resolve).catch(reject);
        }
      });
    });
  }

  async createDefaultUser() {
    const bcrypt = require('bcrypt');
    const defaultPassword = await bcrypt.hash('admin123', 10);
    
    return new Promise((resolve, reject) => {
      this.db.run(
        'INSERT OR IGNORE INTO users (username, password_hash, role) VALUES (?, ?, ?)',
        ['admin', defaultPassword, 'admin'],
        (err) => {
          if (err) {
            console.error('Erreur lors de la création de l\'utilisateur par défaut:', err);
            reject(err);
          } else {
            console.log('Utilisateur admin par défaut créé (admin/admin123)');
            resolve();
          }
        }
      );
    });
  }

  // Méthodes utilitaires pour les requêtes
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  close() {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) {
          reject(err);
        } else {
          console.log('Base de données fermée');
          resolve();
        }
      });
    });
  }
}

module.exports = Database;

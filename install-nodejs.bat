@echo off
title Installation Node.js - Application de Gestion des Importations
color 0E

echo.
echo ========================================
echo   INSTALLATION NODE.JS
echo ========================================
echo.

echo 🔍 Vérification de Node.js...
echo.

REM Tester si Node.js est déjà installé
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js est déjà installé :
    node --version
    echo.
    npm --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ npm est disponible :
        npm --version
        echo.
        echo 🚀 Vous pouvez maintenant lancer l'application !
        echo.
        pause
        exit /b 0
    )
)

echo ❌ Node.js n'est pas installé ou pas dans le PATH
echo.
echo 📥 Téléchargement de Node.js...
echo.

REM Créer un répertoire temporaire
if not exist "temp" mkdir temp

echo Ouverture de la page de téléchargement Node.js...
start https://nodejs.org/

echo.
echo ========================================
echo   INSTRUCTIONS D'INSTALLATION
echo ========================================
echo.
echo 1. 📥 Téléchargez la version LTS (recommandée)
echo 2. 🔧 Exécutez le fichier .msi téléchargé
echo 3. ✅ Suivez l'assistant d'installation
echo 4. ⚠️  IMPORTANT: Cochez "Add to PATH" si demandé
echo 5. 🔄 Redémarrez votre ordinateur après installation
echo 6. 🚀 Relancez ce script pour vérifier
echo.
echo ========================================
echo.

pause

REM Vérifier à nouveau après installation
echo.
echo 🔍 Nouvelle vérification...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js installé avec succès !
    node --version
    npm --version
    echo.
    echo 🚀 Installation terminée !
) else (
    echo ⚠️  Node.js n'est pas encore détecté
    echo.
    echo Solutions possibles :
    echo 1. Redémarrez votre ordinateur
    echo 2. Ouvrez une nouvelle fenêtre de commande
    echo 3. Vérifiez que Node.js est dans le PATH
)

echo.
pause

# Guide d'Installation - Application de Gestion des Importations

## 🔧 Prérequis

### Système d'Exploitation
- **Windows** : Windows 10 ou supérieur
- **macOS** : macOS 10.14 ou supérieur  
- **Linux** : Ubuntu 18.04+ ou équivalent

### Logiciels Requis

#### Node.js (Obligatoire)
- **Version** : Node.js 16.0 ou supérieur
- **Téléchargement** : [https://nodejs.org/](https://nodejs.org/)
- **Vérification** : `node --version` et `npm --version`

#### Git (Optionnel)
- Pour cloner le projet depuis un dépôt
- **Téléchargement** : [https://git-scm.com/](https://git-scm.com/)

## 📥 Installation

### Méthode 1 : Installation Rapide (Recommandée)

#### Windows
1. Téléchargez et décompressez l'archive du projet
2. Double-cliquez sur `start.bat`
3. L'installation se fait automatiquement

#### Linux/macOS
1. Téléchargez et décompressez l'archive du projet
2. Ouvrez un terminal dans le dossier
3. Exécutez : `./start.sh`

### Méthode 2 : Installation Manuelle

#### 1. Obtenir le Code Source
```bash
# Option A: Cloner depuis Git (si disponible)
git clone <repository-url>
cd import-management-app

# Option B: Télécharger et décompresser l'archive
# Puis naviguer dans le dossier
```

#### 2. Installer les Dépendances
```bash
# Installer toutes les dépendances Node.js
npm install

# Ou avec Yarn (si préféré)
yarn install
```

#### 3. Vérifier l'Installation
```bash
# Tester le démarrage
npm run dev
```

## 🚀 Premier Démarrage

### Lancement de l'Application
```bash
# Mode normal
npm start

# Mode développement (avec DevTools)
npm run dev
```

### Connexion Initiale
- **Utilisateur** : `admin`
- **Mot de passe** : `admin123`

### Vérification du Fonctionnement
1. L'application s'ouvre dans une fenêtre Electron
2. La page de connexion s'affiche
3. Après connexion, le tableau de bord apparaît
4. La base de données SQLite est créée automatiquement dans `data/`

## 🗂️ Structure des Fichiers

Après installation, votre dossier contient :

```
import-management-app/
├── main.js                    # Point d'entrée Electron
├── preload.js                 # Script de préchargement
├── package.json               # Configuration du projet
├── start.bat                  # Script de démarrage Windows
├── start.sh                   # Script de démarrage Linux/Mac
├── node_modules/              # Dépendances (créé après npm install)
├── data/                      # Base de données (créé au premier lancement)
│   └── import_management.db
├── src/
│   ├── database/
│   │   └── database.js
│   ├── services/
│   │   ├── authService.js
│   │   ├── orderService.js
│   │   └── excelService.js
│   └── renderer/
│       ├── index.html
│       ├── styles/main.css
│       └── js/app.js
└── assets/
    └── icon.png
```

## 🔧 Configuration

### Base de Données
- **Type** : SQLite
- **Emplacement** : `data/import_management.db`
- **Création** : Automatique au premier lancement
- **Sauvegarde** : Copiez le fichier `.db`

### Paramètres par Défaut
- **Port** : Aucun (application desktop)
- **Utilisateur admin** : Créé automatiquement
- **Langue** : Français
- **Devise** : DZD (Dinar Algérien)

### Personnalisation
Modifiez ces fichiers selon vos besoins :
- `src/renderer/styles/main.css` : Apparence
- `src/database/database.js` : Schéma de base de données
- `main.js` : Configuration de la fenêtre

## 🛠️ Dépannage

### Problèmes Courants

#### "node n'est pas reconnu"
**Cause** : Node.js n'est pas installé ou pas dans le PATH
**Solution** :
1. Téléchargez Node.js depuis [nodejs.org](https://nodejs.org/)
2. Installez avec les options par défaut
3. Redémarrez votre terminal/invite de commande
4. Vérifiez : `node --version`

#### "npm install échoue"
**Cause** : Problème de réseau ou de permissions
**Solutions** :
```bash
# Nettoyer le cache npm
npm cache clean --force

# Supprimer node_modules et réinstaller
rm -rf node_modules
npm install

# Ou utiliser yarn
npm install -g yarn
yarn install
```

#### "L'application ne se lance pas"
**Causes possibles** :
1. **Dépendances manquantes** : Exécutez `npm install`
2. **Port occupé** : Fermez d'autres instances
3. **Permissions** : Exécutez en tant qu'administrateur (Windows)

#### "Erreur de base de données"
**Solutions** :
1. Vérifiez que le dossier `data/` existe
2. Supprimez `data/import_management.db` pour recréer
3. Vérifiez les permissions d'écriture

#### "Import Excel ne fonctionne pas"
**Vérifications** :
1. Format de fichier : `.xlsx` ou `.xls`
2. Première ligne = en-têtes de colonnes
3. Données dans les bonnes colonnes
4. Pas de cellules fusionnées

### Logs et Débogage

#### Mode Développement
```bash
npm run dev
```
- Ouvre les DevTools automatiquement
- Logs détaillés dans la console
- Rechargement automatique

#### Fichiers de Log
- **Console Electron** : F12 dans l'application
- **Terminal** : Sortie de `npm start`
- **Base de données** : Erreurs SQLite affichées

### Performance

#### Optimisation
- **RAM recommandée** : 4 GB minimum
- **Espace disque** : 500 MB pour l'application + données
- **Processeur** : Tout processeur moderne

#### Limites
- **Commandes** : Testé jusqu'à 10,000 commandes
- **Fichiers Excel** : Jusqu'à 1 MB recommandé
- **Utilisateurs simultanés** : 1 (application desktop)

## 🔄 Mise à Jour

### Mise à Jour Manuelle
1. Sauvegardez vos données :
   ```bash
   # Copiez le dossier data/
   cp -r data/ data_backup/
   ```

2. Téléchargez la nouvelle version
3. Remplacez les fichiers (sauf `data/`)
4. Réinstallez les dépendances :
   ```bash
   npm install
   ```

### Migration de Données
- Les migrations sont automatiques
- Sauvegardez toujours avant une mise à jour majeure
- Testez sur une copie en cas de doute

## 📦 Distribution

### Créer un Exécutable
```bash
# Pour Windows
npm run build:win

# Pour macOS
npm run build:mac

# Pour Linux
npm run build:linux

# Pour toutes les plateformes
npm run build
```

### Fichiers de Distribution
- **Windows** : `.exe` dans `dist/`
- **macOS** : `.dmg` dans `dist/`
- **Linux** : `.AppImage` dans `dist/`

## 🆘 Support

### Ressources
- **Documentation** : `README.md` et `GUIDE_UTILISATION.md`
- **Exemples** : `test-data.sql` pour données de test
- **Template Excel** : `npm run create-template`

### Problèmes Fréquents
1. **Lenteur** : Utilisez les filtres pour de gros volumes
2. **Crash** : Vérifiez les logs, redémarrez l'application
3. **Données perdues** : Vérifiez `data/import_management.db`

### Contact
- Créez un ticket avec les détails du problème
- Incluez les logs d'erreur
- Précisez votre système d'exploitation et version Node.js

// Script pour insérer des données de test avancées
const Database = require('./src/database/database');
const OrderService = require('./src/services/orderService');

async function insertAdvancedTestData() {
    console.log('🚀 Insertion des données de test avancées...');
    
    const database = new Database();
    await database.initialize();
    
    const orderService = new OrderService(database);

    // Données de test pour commande complète
    const testOrders = [
        {
            // Commande 1 - Maritime avec articles et coûts complets
            order_number: 'CMD-ADV-' + Date.now() + '-001',
            order_date: '2024-01-15',
            supplier_name: 'Shanghai Electronics Manufacturing Co.',
            shipment_type: 'SEA',
            invoice_number: 'INV-SHA-2024-001',
            invoice_date: '2024-01-10',
            from_location: 'Shanghai Port',
            to_location: 'Port d\'Alger',
            operation_type: 'Import Commercial',
            goods_description: 'Composants électroniques et circuits intégrés',
            bank_name: 'Banque Nationale d\'Algérie',
            lc_number: 'LC-BNA-2024-001',
            lc_validation_date: '2024-01-05',
            payment_term: '30 jours après B/L',
            price_term: 'FOB Shanghai',
            quantity_pcs: 5000,
            num_containers_20ft: 2,
            num_containers_40ft: 1,
            num_packages: 150,
            exchange_rate_usd_dzd: 134.50,
            exchange_rate_eur_dzd: 145.20,
            exchange_rate_yuan_dzd: 19.80,
            fob_amount_currency: 75000.00,
            fob_currency: 'USD',
            freight_amount_currency: 4500.00,
            total_cif_currency: 79500.00,
            
            // Articles détaillés
            items: [
                {
                    item_number: 1,
                    part_number: 'MCU-ARM-001',
                    description: 'Microcontrôleur ARM Cortex-M4',
                    quantity: 1000,
                    unit_fob: 25.00,
                    amount: 25000.00
                },
                {
                    item_number: 2,
                    part_number: 'MEM-DDR4-8GB',
                    description: 'Mémoire DDR4 8GB SO-DIMM',
                    quantity: 500,
                    unit_fob: 45.00,
                    amount: 22500.00
                },
                {
                    item_number: 3,
                    part_number: 'SEN-TEMP-001',
                    description: 'Capteur de température numérique',
                    quantity: 2000,
                    unit_fob: 8.50,
                    amount: 17000.00
                },
                {
                    item_number: 4,
                    part_number: 'PCB-MAIN-V2',
                    description: 'Circuit imprimé principal v2.0',
                    quantity: 1500,
                    unit_fob: 7.00,
                    amount: 10500.00
                }
            ],
            
            // Avis d'arrivée
            arrivalNotices: [
                {
                    voyage_number: 'MSC-2024-001',
                    bill_of_lading_number: 'MSCU-SHA-ALG-240115',
                    vessel_name: 'MSC Mediterranea',
                    shipowner: 'Mediterranean Shipping Company',
                    actual_time_of_arrival: '2024-02-20'
                }
            ],
            
            // Allocations de coûts
            costAllocations: [
                {
                    cost_type: 'CustomsDuties1',
                    cost_name: 'DROITS DE DOUANE ALGERIA',
                    invoice_number: 'CUST-ALG-001',
                    invoice_date: '2024-02-21',
                    total_ttc: 850000.00,
                    tva: 144068.00,
                    ht: 705932.00,
                    is_manual_entry: false
                },
                {
                    cost_type: 'ImportDelivery',
                    cost_name: 'LIVRAISON IMPORT ALGER',
                    invoice_number: 'DEL-ALG-001',
                    invoice_date: '2024-02-22',
                    total_ttc: 180000.00,
                    tva: 30508.00,
                    ht: 149492.00,
                    is_manual_entry: false
                },
                {
                    cost_type: 'ShippingAgencyServices',
                    cost_name: 'SERVICES AGENCE MARITIME',
                    invoice_number: 'SHIP-ALG-001',
                    invoice_date: '2024-02-21',
                    total_ttc: 95000.00,
                    tva: 16102.00,
                    ht: 78898.00,
                    is_manual_entry: false
                },
                {
                    cost_type: 'OtherMiscellaneous',
                    cost_name: 'SURESTARIES CONTENEURS',
                    invoice_number: 'DEM-ALG-001',
                    invoice_date: '2024-02-23',
                    total_ttc: 45000.00,
                    tva: 0.00,
                    ht: 45000.00,
                    is_manual_entry: true
                }
            ]
        },
        
        {
            // Commande 2 - Aérien avec coûts différents
            order_number: 'CMD-ADV-' + (Date.now() + 1) + '-002',
            order_date: '2024-01-25',
            supplier_name: 'German Precision Tools GmbH',
            shipment_type: 'AIR',
            invoice_number: 'INV-GER-2024-002',
            invoice_date: '2024-01-22',
            from_location: 'Frankfurt Airport',
            to_location: 'Aéroport Houari Boumediene',
            operation_type: 'Import Industriel',
            goods_description: 'Outils de précision et machines-outils',
            bank_name: 'Crédit Populaire d\'Algérie',
            lc_number: 'LC-CPA-2024-002',
            lc_validation_date: '2024-01-18',
            payment_term: '60 jours après réception',
            price_term: 'FOB Frankfurt',
            quantity_pcs: 25,
            num_containers_20ft: 0,
            num_containers_40ft: 0,
            num_packages: 8,
            exchange_rate_usd_dzd: 134.50,
            exchange_rate_eur_dzd: 145.20,
            exchange_rate_yuan_dzd: 19.80,
            fob_amount_currency: 35000.00,
            fob_currency: 'EUR',
            freight_amount_currency: 2800.00,
            total_cif_currency: 37800.00,
            
            items: [
                {
                    item_number: 1,
                    part_number: 'TOOL-PREC-001',
                    description: 'Fraiseuse CNC de précision',
                    quantity: 2,
                    unit_fob: 12000.00,
                    amount: 24000.00
                },
                {
                    item_number: 2,
                    part_number: 'TOOL-MEAS-001',
                    description: 'Instruments de mesure laser',
                    quantity: 5,
                    unit_fob: 1800.00,
                    amount: 9000.00
                },
                {
                    item_number: 3,
                    part_number: 'TOOL-CUT-001',
                    description: 'Outils de coupe carbure',
                    quantity: 18,
                    unit_fob: 111.11,
                    amount: 2000.00
                }
            ],
            
            arrivalNotices: [
                {
                    voyage_number: 'LH-CARGO-2024-002',
                    bill_of_lading_number: 'AWB-FRA-ALG-240125',
                    vessel_name: 'Lufthansa Cargo Flight',
                    shipowner: 'Lufthansa Cargo AG',
                    actual_time_of_arrival: '2024-01-27'
                }
            ],
            
            costAllocations: [
                {
                    cost_type: 'CustomsDuties1',
                    cost_name: 'DROITS DE DOUANE ALGERIA',
                    invoice_number: 'CUST-ALG-002',
                    invoice_date: '2024-01-28',
                    total_ttc: 420000.00,
                    tva: 71186.00,
                    ht: 348814.00,
                    is_manual_entry: false
                },
                {
                    cost_type: 'ImportDelivery',
                    cost_name: 'LIVRAISON IMPORT EXPRESS',
                    invoice_number: 'DEL-ALG-002',
                    invoice_date: '2024-01-29',
                    total_ttc: 85000.00,
                    tva: 14407.00,
                    ht: 70593.00,
                    is_manual_entry: false
                }
            ]
        },
        
        {
            // Commande 3 - Express avec coûts minimaux
            order_number: 'CMD-ADV-' + (Date.now() + 2) + '-003',
            order_date: '2024-02-05',
            supplier_name: 'Beijing Auto Parts Limited',
            shipment_type: 'Express',
            invoice_number: 'INV-BEJ-2024-003',
            invoice_date: '2024-02-02',
            from_location: 'Beijing Capital Airport',
            to_location: 'Aéroport d\'Oran',
            operation_type: 'Import Urgent',
            goods_description: 'Pièces détachées automobiles urgentes',
            bank_name: 'BADR Bank',
            lc_number: 'LC-BADR-2024-003',
            lc_validation_date: '2024-01-30',
            payment_term: '15 jours après expédition',
            price_term: 'FOB Beijing',
            quantity_pcs: 500,
            num_containers_20ft: 0,
            num_containers_40ft: 0,
            num_packages: 12,
            exchange_rate_usd_dzd: 134.50,
            exchange_rate_eur_dzd: 145.20,
            exchange_rate_yuan_dzd: 19.80,
            fob_amount_currency: 18000.00,
            fob_currency: 'CNY',
            freight_amount_currency: 2200.00,
            total_cif_currency: 20200.00,
            
            items: [
                {
                    item_number: 1,
                    part_number: 'AUTO-BRAKE-001',
                    description: 'Plaquettes de frein avant',
                    quantity: 100,
                    unit_fob: 85.00,
                    amount: 8500.00
                },
                {
                    item_number: 2,
                    part_number: 'AUTO-FILTER-001',
                    description: 'Filtres à huile moteur',
                    quantity: 200,
                    unit_fob: 25.00,
                    amount: 5000.00
                },
                {
                    item_number: 3,
                    part_number: 'AUTO-BELT-001',
                    description: 'Courroies de distribution',
                    quantity: 50,
                    unit_fob: 90.00,
                    amount: 4500.00
                }
            ],
            
            arrivalNotices: [
                {
                    voyage_number: 'DHL-EXPRESS-2024-003',
                    bill_of_lading_number: 'DHL-BEJ-ORA-240205',
                    vessel_name: 'DHL Express Flight',
                    shipowner: 'DHL Express',
                    actual_time_of_arrival: '2024-02-07'
                }
            ],
            
            costAllocations: [
                {
                    cost_type: 'CustomsDuties1',
                    cost_name: 'DROITS DE DOUANE EXPRESS',
                    invoice_number: 'CUST-ALG-003',
                    invoice_date: '2024-02-08',
                    total_ttc: 65000.00,
                    tva: 11017.00,
                    ht: 53983.00,
                    is_manual_entry: false
                },
                {
                    cost_type: 'ImportDelivery',
                    cost_name: 'LIVRAISON EXPRESS ORAN',
                    invoice_number: 'DEL-ORA-003',
                    invoice_date: '2024-02-08',
                    total_ttc: 35000.00,
                    tva: 5932.00,
                    ht: 29068.00,
                    is_manual_entry: false
                }
            ]
        }
    ];

    // Insérer les commandes de test
    for (const orderData of testOrders) {
        try {
            console.log(`📦 Création de la commande ${orderData.order_number}...`);
            const result = await orderService.createOrder(orderData);
            console.log(`✅ Commande ${orderData.order_number} créée avec succès (ID: ${result.id})`);
            
            // Afficher les calculs
            console.log(`   💰 CIF DZD: ${result.total_cif_dzd?.toLocaleString('fr-FR')} DZD`);
            console.log(`   🏭 Coût d'atterrissage: ${result.landed_cost_ht?.toLocaleString('fr-FR')} DZD`);
            console.log(`   📊 Coefficient: ${result.landed_cost_coefficient?.toFixed(4)}`);
            console.log(`   📋 Articles: ${result.items?.length || 0}`);
            console.log(`   🚢 Avis d'arrivée: ${result.arrivalNotices?.length || 0}`);
            console.log(`   💸 Allocations: ${result.costAllocations?.length || 0}`);
            console.log('');
            
        } catch (error) {
            console.error(`❌ Erreur lors de la création de ${orderData.order_number}:`, error.message);
        }
    }

    await database.close();
    console.log('🎉 Insertion des données de test terminée !');
    console.log('');
    console.log('📊 Résumé des données insérées :');
    console.log('   • 3 commandes complètes avec calculs automatiques');
    console.log('   • 10 articles avec coûts de revient calculés');
    console.log('   • 3 avis d\'arrivée pour suivi logistique');
    console.log('   • 7 allocations de coûts avec différents types');
    console.log('   • Données multi-devises (USD, EUR, Yuan)');
    console.log('   • Types d\'expédition variés (Maritime, Aérien, Express)');
    console.log('');
    console.log('🚀 Vous pouvez maintenant tester toutes les fonctionnalités avancées !');
}

// Exécuter le script
insertAdvancedTestData().catch(console.error);

const XLSX = require('xlsx');
const path = require('path');

class ExcelService {
  constructor() {
    this.workbook = null;
  }

  async importFromExcel(filePath) {
    try {
      // Lire le fichier Excel
      this.workbook = XLSX.readFile(filePath);
      const sheetNames = this.workbook.SheetNames;
      
      if (sheetNames.length === 0) {
        throw new Error('Le fichier Excel ne contient aucune feuille');
      }

      // Utiliser la première feuille par défaut
      const worksheet = this.workbook.Sheets[sheetNames[0]];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (jsonData.length < 2) {
        throw new Error('Le fichier Excel doit contenir au moins une ligne d\'en-tête et une ligne de données');
      }

      // Analyser les données et les convertir au format de l'application
      const headers = jsonData[0];
      const rows = jsonData.slice(1);

      const orders = [];
      
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        if (row.length === 0 || !row[0]) continue; // Ignorer les lignes vides

        const order = this.mapRowToOrder(headers, row, i + 2); // +2 car ligne 1 = headers, ligne 2 = première donnée
        if (order) {
          orders.push(order);
        }
      }

      return {
        orders,
        totalRows: rows.length,
        importedRows: orders.length,
        fileName: path.basename(filePath)
      };
    } catch (error) {
      throw new Error(`Erreur lors de l'import Excel: ${error.message}`);
    }
  }

  mapRowToOrder(headers, row, rowNumber) {
    try {
      // Mapping des colonnes Excel vers les champs de la base de données
      const columnMapping = {
        'order_number': ['Numéro Commande', 'Order Number', 'N° Commande'],
        'order_date': ['Date Commande', 'Order Date', 'Date'],
        'supplier_name': ['Fournisseur', 'Supplier', 'Supplier Name'],
        'shipment_type': ['Type Expédition', 'Shipment Type', 'Transport'],
        'invoice_number': ['N° Facture', 'Invoice Number', 'Facture'],
        'invoice_date': ['Date Facture', 'Invoice Date'],
        'from_location': ['Origine', 'From', 'Départ'],
        'to_location': ['Destination', 'To', 'Arrivée'],
        'goods_description': ['Description', 'Goods', 'Marchandises'],
        'quantity_pcs': ['Quantité', 'Quantity', 'Qté'],
        'fob_amount_currency': ['Montant FOB', 'FOB Amount', 'FOB'],
        'fob_currency': ['Devise FOB', 'FOB Currency', 'Devise'],
        'freight_amount_currency': ['Fret', 'Freight', 'Transport'],
        'exchange_rate_usd_dzd': ['Taux USD', 'USD Rate', 'USD/DZD'],
        'exchange_rate_eur_dzd': ['Taux EUR', 'EUR Rate', 'EUR/DZD']
      };

      const order = {};

      // Mapper chaque champ
      for (const [field, possibleHeaders] of Object.entries(columnMapping)) {
        const columnIndex = this.findColumnIndex(headers, possibleHeaders);
        if (columnIndex !== -1 && row[columnIndex] !== undefined) {
          let value = row[columnIndex];
          
          // Traitement spécial selon le type de champ
          if (field.includes('date')) {
            value = this.parseExcelDate(value);
          } else if (field.includes('amount') || field.includes('rate') || field === 'quantity_pcs') {
            value = this.parseNumber(value);
          } else if (typeof value === 'string') {
            value = value.trim();
          }
          
          order[field] = value;
        }
      }

      // Validation minimale
      if (!order.order_number) {
        console.warn(`Ligne ${rowNumber}: Numéro de commande manquant, ligne ignorée`);
        return null;
      }

      // Valeurs par défaut
      order.shipment_type = order.shipment_type || 'Other';
      order.fob_currency = order.fob_currency || 'USD';
      
      return order;
    } catch (error) {
      console.error(`Erreur lors du mapping de la ligne ${rowNumber}:`, error);
      return null;
    }
  }

  findColumnIndex(headers, possibleNames) {
    for (const name of possibleNames) {
      const index = headers.findIndex(header => 
        header && header.toString().toLowerCase().includes(name.toLowerCase())
      );
      if (index !== -1) return index;
    }
    return -1;
  }

  parseExcelDate(value) {
    if (!value) return null;
    
    // Si c'est déjà une date
    if (value instanceof Date) {
      return value.toISOString().split('T')[0];
    }
    
    // Si c'est un nombre Excel (date sérielle)
    if (typeof value === 'number') {
      const date = XLSX.SSF.parse_date_code(value);
      return `${date.y}-${String(date.m).padStart(2, '0')}-${String(date.d).padStart(2, '0')}`;
    }
    
    // Si c'est une chaîne, essayer de la parser
    if (typeof value === 'string') {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0];
      }
    }
    
    return null;
  }

  parseNumber(value) {
    if (value === null || value === undefined || value === '') return null;
    
    if (typeof value === 'number') return value;
    
    if (typeof value === 'string') {
      // Nettoyer la chaîne (supprimer espaces, virgules comme séparateurs de milliers)
      const cleaned = value.replace(/[^\d.,-]/g, '').replace(',', '.');
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? null : parsed;
    }
    
    return null;
  }

  async exportToExcel(data, filePath) {
    try {
      const { orders } = data;
      
      if (!orders || orders.length === 0) {
        throw new Error('Aucune donnée à exporter');
      }

      // Créer un nouveau classeur
      const workbook = XLSX.utils.book_new();

      // Préparer les données pour l'export des commandes
      const exportData = orders.map(order => ({
        'Numéro Commande': order.order_number,
        'Date Commande': order.order_date,
        'Fournisseur': order.supplier_name,
        'Type Expédition': order.shipment_type,
        'N° Facture': order.invoice_number,
        'Date Facture': order.invoice_date,
        'Origine': order.from_location,
        'Destination': order.to_location,
        'Description': order.goods_description,
        'Quantité': order.quantity_pcs,
        'Montant FOB': order.fob_amount_currency,
        'Devise FOB': order.fob_currency,
        'Fret': order.freight_amount_currency,
        'Total CIF': order.total_cif_currency,
        'Montant FOB DZD': order.fob_amount_dzd,
        'Fret DZD': order.freight_amount_dzd,
        'Total CIF DZD': order.total_cif_dzd,
        'Coût Atterrissage HT': order.landed_cost_ht,
        'Coefficient': order.landed_cost_coefficient,
        'Total Payé TTC': order.total_paid_ttc,
        'Taux USD/DZD': order.exchange_rate_usd_dzd,
        'Taux EUR/DZD': order.exchange_rate_eur_dzd,
        'Taux Yuan/DZD': order.exchange_rate_yuan_dzd,
        'Date Création': order.created_at
      }));

      // Créer la feuille de calcul
      const worksheet = XLSX.utils.json_to_sheet(exportData);

      // Ajuster la largeur des colonnes
      const columnWidths = [
        { wch: 15 }, // Numéro Commande
        { wch: 12 }, // Date Commande
        { wch: 20 }, // Fournisseur
        { wch: 15 }, // Type Expédition
        { wch: 15 }, // N° Facture
        { wch: 12 }, // Date Facture
        { wch: 15 }, // Origine
        { wch: 15 }, // Destination
        { wch: 30 }, // Description
        { wch: 10 }, // Quantité
        { wch: 15 }, // Montant FOB
        { wch: 8 },  // Devise FOB
        { wch: 15 }, // Fret
        { wch: 15 }, // Total CIF
        { wch: 15 }, // Montant FOB DZD
        { wch: 15 }, // Fret DZD
        { wch: 15 }, // Total CIF DZD
        { wch: 18 }, // Coût Atterrissage HT
        { wch: 12 }, // Coefficient
        { wch: 15 }, // Total Payé TTC
        { wch: 12 }, // Taux USD/DZD
        { wch: 12 }, // Taux EUR/DZD
        { wch: 15 }  // Date Création
      ];
      worksheet['!cols'] = columnWidths;

      // Ajouter la feuille au classeur
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Commandes');

      // Export des articles avec coûts de revient
      const allItems = [];
      orders.forEach(order => {
        if (order.items && order.items.length > 0) {
          order.items.forEach(item => {
            allItems.push({
              'Numéro Commande': order.order_number,
              'N° Article': item.item_number,
              'Référence': item.part_number,
              'Description': item.description,
              'Quantité': item.quantity,
              'Prix Unitaire FOB': item.unit_fob,
              'Montant FOB': item.amount,
              'Prix Unit. FOB DZD': item.unit_fob_dzd,
              'Montant FOB DZD': item.amount_fob_dzd,
              'Prix Unit. Revient': item.unit_cost_price,
              'Coût Total Revient': item.total_cost_price,
              'Surcoût Unitaire': item.unit_cost_price - item.unit_fob_dzd,
              'Coefficient Appliqué': order.landed_cost_coefficient
            });
          });
        }
      });

      if (allItems.length > 0) {
        const itemsWorksheet = XLSX.utils.json_to_sheet(allItems);
        itemsWorksheet['!cols'] = [
          { wch: 15 }, // Numéro Commande
          { wch: 10 }, // N° Article
          { wch: 20 }, // Référence
          { wch: 30 }, // Description
          { wch: 10 }, // Quantité
          { wch: 15 }, // Prix Unitaire FOB
          { wch: 15 }, // Montant FOB
          { wch: 15 }, // Prix Unit. FOB DZD
          { wch: 15 }, // Montant FOB DZD
          { wch: 15 }, // Prix Unit. Revient
          { wch: 15 }, // Coût Total Revient
          { wch: 15 }, // Surcoût Unitaire
          { wch: 12 }  // Coefficient Appliqué
        ];
        XLSX.utils.book_append_sheet(workbook, itemsWorksheet, 'Articles_Coûts_Revient');
      }

      // Export des allocations de coûts
      const allCostAllocations = [];
      orders.forEach(order => {
        if (order.costAllocations && order.costAllocations.length > 0) {
          order.costAllocations.forEach(cost => {
            allCostAllocations.push({
              'Numéro Commande': order.order_number,
              'Type de Coût': cost.cost_type,
              'Nom du Coût': cost.cost_name,
              'N° Facture': cost.invoice_number,
              'Date Facture': cost.invoice_date,
              'Montant HT': cost.ht,
              'TVA': cost.tva,
              'Total TTC': cost.total_ttc,
              'Saisie Manuelle': cost.is_manual_entry ? 'Oui' : 'Non'
            });
          });
        }
      });

      if (allCostAllocations.length > 0) {
        const costsWorksheet = XLSX.utils.json_to_sheet(allCostAllocations);
        costsWorksheet['!cols'] = [
          { wch: 15 }, // Numéro Commande
          { wch: 20 }, // Type de Coût
          { wch: 25 }, // Nom du Coût
          { wch: 15 }, // N° Facture
          { wch: 12 }, // Date Facture
          { wch: 15 }, // Montant HT
          { wch: 12 }, // TVA
          { wch: 15 }, // Total TTC
          { wch: 12 }  // Saisie Manuelle
        ];
        XLSX.utils.book_append_sheet(workbook, costsWorksheet, 'Allocations_Coûts');
      }

      // Export des avis d'arrivée
      const allArrivalNotices = [];
      orders.forEach(order => {
        if (order.arrivalNotices && order.arrivalNotices.length > 0) {
          order.arrivalNotices.forEach(notice => {
            allArrivalNotices.push({
              'Numéro Commande': order.order_number,
              'N° Voyage': notice.voyage_number,
              'N° Connaissement': notice.bill_of_lading_number,
              'Nom Navire': notice.vessel_name,
              'Armateur': notice.shipowner,
              'Date Arrivée': notice.actual_time_of_arrival
            });
          });
        }
      });

      if (allArrivalNotices.length > 0) {
        const noticesWorksheet = XLSX.utils.json_to_sheet(allArrivalNotices);
        noticesWorksheet['!cols'] = [
          { wch: 15 }, // Numéro Commande
          { wch: 15 }, // N° Voyage
          { wch: 20 }, // N° Connaissement
          { wch: 20 }, // Nom Navire
          { wch: 20 }, // Armateur
          { wch: 12 }  // Date Arrivée
        ];
        XLSX.utils.book_append_sheet(workbook, noticesWorksheet, 'Avis_Arrivée');
      }

      // Écrire le fichier
      XLSX.writeFile(workbook, filePath);

      return {
        success: true,
        filePath,
        exportedOrders: orders.length,
        exportedItems: allItems.length
      };
    } catch (error) {
      throw new Error(`Erreur lors de l'export Excel: ${error.message}`);
    }
  }

  // Méthode pour créer un template Excel
  async createTemplate(filePath) {
    try {
      const templateData = [{
        'Numéro Commande': 'CMD001',
        'Date Commande': '2024-01-15',
        'Fournisseur': 'Fournisseur Exemple',
        'Type Expédition': 'SEA',
        'N° Facture': 'INV001',
        'Date Facture': '2024-01-10',
        'Origine': 'Shanghai',
        'Destination': 'Alger',
        'Description': 'Pièces automobiles',
        'Quantité': 1000,
        'Montant FOB': 50000,
        'Devise FOB': 'USD',
        'Fret': 5000,
        'Taux USD/DZD': 134.5,
        'Taux EUR/DZD': 145.2
      }];

      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(templateData);
      
      // Largeurs de colonnes pour le template
      worksheet['!cols'] = [
        { wch: 15 }, { wch: 12 }, { wch: 20 }, { wch: 15 }, { wch: 15 },
        { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 30 }, { wch: 10 },
        { wch: 15 }, { wch: 8 }, { wch: 15 }, { wch: 12 }, { wch: 12 }
      ];

      XLSX.utils.book_append_sheet(workbook, worksheet, 'Template');
      XLSX.writeFile(workbook, filePath);

      return { success: true, filePath };
    } catch (error) {
      throw new Error(`Erreur lors de la création du template: ${error.message}`);
    }
  }
}

module.exports = ExcelService;

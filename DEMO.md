# 🎯 Démonstration - Application de Gestion des Importations

## 🚀 Démarrage Rapide de la Démo

### 1. Lancement
```bash
# Démarrer l'application
npm run dev
```

### 2. Connexion
- **Utilisateur** : `admin`
- **Mot de passe** : `admin123`

## 📋 Scénario de Démonstration

### Étape 1 : Découverte du Tableau de Bord
1. **Connexion** avec les identifiants par défaut
2. **Observation** du tableau de bord vide (première utilisation)
3. **Navigation** dans les différentes sections via la sidebar

### Étape 2 : Création d'une Première Commande
1. Cliquez sur **"Nouvelle commande"**
2. Remplissez les informations :

#### Informations Générales
- **Numéro de Commande** : `CMD-DEMO-001`
- **Date de Commande** : Date d'aujourd'hui
- **Nom du Fournisseur** : `Shanghai Electronics Co.`
- **Type d'Expédition** : `Maritime (SEA)`
- **Origine** : `Shanghai`
- **Destination** : `Alger`
- **Description** : `Composants électroniques pour assemblage`

#### Informations Financières
- **Montant FOB** : `50000`
- **Devise FOB** : `USD`
- **Montant Fret** : `3000`
- **Taux USD/DZD** : `134.50`
- **Taux EUR/DZD** : `145.20`

3. **Enregistrer** la commande

### Étape 3 : Vérification des Calculs Automatiques
1. Retournez au **tableau de bord**
2. Observez les **statistiques mises à jour** :
   - Total commandes : 1
   - Valeur totale : 7,128,500 DZD (calculé automatiquement)
   - Commandes du mois : 1

3. Vérifiez la **commande récente** dans le tableau

### Étape 4 : Création de Commandes Supplémentaires

#### Commande 2 - Transport Aérien
- **Numéro** : `CMD-DEMO-002`
- **Fournisseur** : `German Machinery GmbH`
- **Type** : `Aérien (AIR)`
- **Origine** : `Hamburg`
- **Destination** : `Alger`
- **FOB** : `25000 EUR`
- **Fret** : `2000 EUR`

#### Commande 3 - Express
- **Numéro** : `CMD-DEMO-003`
- **Fournisseur** : `Beijing Auto Parts Ltd.`
- **Type** : `Express`
- **Origine** : `Beijing`
- **Destination** : `Oran`
- **FOB** : `15000 CNY`
- **Fret** : `1500 CNY`

### Étape 5 : Exploration de la Liste des Commandes
1. Allez dans **"Commandes"**
2. Observez les **3 commandes créées**
3. Testez les **fonctionnalités de filtrage** :
   - Recherche par fournisseur : `Shanghai`
   - Filtre par type : `Maritime`
   - Filtre par date

### Étape 6 : Modification d'une Commande
1. Cliquez sur l'**icône crayon** d'une commande
2. Modifiez quelques informations
3. **Enregistrez** les modifications
4. Vérifiez que les **calculs sont mis à jour**

### Étape 7 : Démonstration Import/Export Excel
1. Allez dans **"Import/Export"**
2. Cliquez sur **"Exporter vers Excel"**
3. Sauvegardez le fichier
4. Ouvrez le fichier Excel pour voir les données exportées

## 🎨 Points Forts à Démontrer

### Interface Utilisateur
- ✅ **Design moderne** avec Bootstrap 5
- ✅ **Navigation intuitive** avec sidebar
- ✅ **Responsive design** adaptable
- ✅ **Icônes Bootstrap** pour une meilleure UX

### Fonctionnalités Métier
- ✅ **Calculs automatiques** des montants en DZD
- ✅ **Gestion multi-devises** (USD, EUR, Yuan)
- ✅ **Types d'expédition** variés
- ✅ **Validation des données** en temps réel

### Performance et Fiabilité
- ✅ **Base de données SQLite** intégrée
- ✅ **Sauvegarde automatique** des données
- ✅ **Gestion d'erreurs** robuste
- ✅ **Interface réactive** sans latence

### Sécurité
- ✅ **Authentification** avec hashage des mots de passe
- ✅ **Isolation des contextes** Electron
- ✅ **Validation côté serveur** et client

## 📊 Données de Démonstration Avancée

### Script de Données de Test
Pour une démonstration plus riche, vous pouvez exécuter :

```bash
# Créer des données de test (optionnel)
sqlite3 data/import_management.db < test-data.sql
```

Cela ajoutera :
- **3 commandes complètes** avec articles détaillés
- **Avis d'arrivée** pour chaque commande
- **Allocations de coûts** (douanes, livraison, etc.)
- **Calculs complets** de coûts d'atterrissage

### Résultats Attendus
Après insertion des données de test :
- **Total commandes** : 6 (3 créées + 3 de test)
- **Valeur totale** : ~18,000,000 DZD
- **Différents types** d'expédition représentés
- **Données complètes** pour tous les rapports

## 🎯 Scénarios d'Utilisation Métier

### Scénario 1 : Importateur de Composants Électroniques
**Contexte** : Entreprise important des composants depuis l'Asie
**Démonstration** :
- Commandes multiples depuis la Chine
- Calculs de coûts d'atterrissage précis
- Suivi des taux de change Yuan/DZD
- Export pour comptabilité

### Scénario 2 : Importateur de Machines Industrielles
**Contexte** : Import de machines lourdes depuis l'Europe
**Démonstration** :
- Transport maritime pour gros volumes
- Calculs en EUR avec taux de change
- Gestion des conteneurs 20ft/40ft
- Suivi des avis d'arrivée

### Scénario 3 : Import Express de Pièces Détachées
**Contexte** : Livraisons urgentes pour maintenance
**Démonstration** :
- Transport express/aérien
- Coûts plus élevés mais délais courts
- Gestion des petites quantités
- Traçabilité complète

## 🔍 Points Techniques à Souligner

### Architecture Electron
- **Main Process** : Gestion de la base de données et logique métier
- **Renderer Process** : Interface utilisateur moderne
- **IPC sécurisé** : Communication entre processus
- **Préchargement** : Scripts sécurisés pour l'API

### Base de Données SQLite
- **Schéma relationnel** complet
- **Contraintes d'intégrité** (clés étrangères, uniques)
- **Index** pour les performances
- **Transactions** pour la cohérence

### Calculs Financiers
- **Conversion automatique** selon les taux de change
- **Formules métier** intégrées :
  - FOB DZD = FOB devise × Taux
  - Coût d'atterrissage = CIF + Allocations
  - Coefficient = Coût d'atterrissage / FOB

## 🎪 Présentation Recommandée

### Introduction (2 minutes)
1. **Contexte** : Problématique de gestion des importations
2. **Solution** : Application desktop avec Electron + SQLite
3. **Avantages** : Autonomie, sécurité, performance

### Démonstration Live (10 minutes)
1. **Connexion** et découverte de l'interface
2. **Création** d'une commande complète
3. **Navigation** et filtrage des données
4. **Export Excel** pour intégration comptable
5. **Modification** et mise à jour en temps réel

### Questions Techniques (5 minutes)
- Architecture et choix technologiques
- Sécurité et sauvegarde des données
- Possibilités d'extension et personnalisation
- Déploiement et maintenance

## 🚀 Extensions Possibles

### Fonctionnalités Avancées
- **Rapports PDF** automatisés
- **Graphiques** et tableaux de bord avancés
- **Notifications** pour les échéances
- **Synchronisation cloud** (optionnelle)

### Intégrations
- **API Odoo** pour synchronisation ERP
- **Services bancaires** pour taux de change automatiques
- **Douanes électroniques** pour déclarations
- **Transporteurs** pour suivi des expéditions

### Personnalisation
- **Thèmes** et couleurs d'entreprise
- **Champs personnalisés** selon les besoins
- **Workflows** spécifiques par secteur
- **Multi-langues** (arabe, anglais, français)
